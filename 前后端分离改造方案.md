# 药店管理系统前后端分离改造方案

## 项目概述

将现有的Spring Boot + Thymeleaf单体应用改造为前后端分离架构：
- **后端**: Spring Boot REST API
- **前端**: Vue.js 3 + Element Plus
- **保持**: 完全兼容现有业务逻辑和交互

## 系统架构分析

### 当前架构
- Spring Boot 2.5.14 + Thymeleaf模板引擎
- Spring Security安全框架
- JPA + MySQL数据库
- 文件上传功能
- 多药店管理

### 核心业务模块
1. **用户管理**: 登录认证、用户权限、多药店关联
2. **药店管理**: 药店CRUD、药店选择切换
3. **患者管理**: 患者信息、头像上传、搜索
4. **就诊记录**: 病历录入、图片上传、历史记录
5. **快捷药品**: 药品模板管理
6. **统计报表**: 数据统计和导出
7. **系统管理**: 数据迁移、备份等

### 数据模型关系
- **SysUser** ↔ **UserPharmacy** ↔ **Pharmacy** (多对多)
- **Patient** → **Pharmacy** (多对一)
- **MedicalRecord** → **Patient** + **Pharmacy** (多对一)
- **MedicalRecordImage** → **MedicalRecord** (多对一)
- **QuickMedicine** (独立实体)

## 改造策略

### 1. 渐进式改造
- 保持现有系统运行
- 逐模块改造，支持新旧系统并存
- 完成后切换到新系统

### 2. API设计原则
- RESTful API设计
- 统一响应格式
- 完整的错误处理
- API版本控制

### 3. 前端技术栈
- Vue 3 + Composition API
- Element Plus UI组件库
- Vue Router路由管理
- Pinia状态管理
- Axios HTTP客户端
- Vite构建工具

## 改造范围

### 后端改造
1. **Controller层**: 改造为REST API
2. **安全配置**: JWT Token认证
3. **文件上传**: 适配前后端分离
4. **跨域配置**: CORS支持
5. **响应格式**: 统一JSON响应

### 前端开发
1. **项目初始化**: Vue 3项目搭建
2. **路由设计**: 对应现有页面结构
3. **组件开发**: 复用现有UI逻辑
4. **状态管理**: 用户状态、药店状态等
5. **API集成**: 对接后端REST API

### 不变部分
- 数据库结构保持不变
- 业务逻辑完全保持
- 用户交互流程不变
- 权限控制逻辑不变

## 技术实现要点

### 1. 认证机制改造
- 从Session改为JWT Token
- 保持现有的用户权限逻辑
- 支持药店切换功能

### 2. 文件上传改造
- 患者头像上传
- 就诊记录图片上传
- 保持现有的文件存储逻辑

### 3. 分页和搜索
- 统一分页响应格式
- 保持现有搜索逻辑

### 4. 数据导出
- Excel导出功能适配
- 保持现有导出格式

## 部署策略

### 开发环境
- 后端: localhost:8080
- 前端: localhost:3000
- 代理配置处理跨域

### 生产环境
- Nginx反向代理
- 前端静态资源部署
- 后端API服务部署

## 风险控制

1. **数据安全**: 改造过程中确保数据完整性
2. **功能完整性**: 逐一验证每个功能点
3. **性能保证**: 确保改造后性能不降低
4. **回滚方案**: 保留原系统作为备份

## 验收标准

1. **功能完整性**: 所有现有功能正常工作
2. **用户体验**: 交互逻辑与原系统一致
3. **性能指标**: 响应时间不超过原系统
4. **安全性**: 通过安全测试
5. **兼容性**: 支持主流浏览器

## 项目收益

1. **技术现代化**: 采用现代前端技术栈
2. **开发效率**: 前后端独立开发
3. **用户体验**: 更好的交互响应
4. **可维护性**: 代码结构更清晰
5. **扩展性**: 便于后续功能扩展
