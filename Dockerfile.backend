# 多阶段构建 - 构建阶段
FROM maven:3.8.6-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom.xml和源代码
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -Pprod

# 运行阶段
FROM openjdk:8-jre-slim

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r pharmacy && useradd -r -g pharmacy pharmacy

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /var/log/pharmacy /var/pharmacy/uploads \
    && chown -R pharmacy:pharmacy /var/log/pharmacy /var/pharmacy

# 从构建阶段复制JAR文件
COPY --from=builder /app/target/pharmacy-management-*.jar app.jar

# 设置文件权限
RUN chown pharmacy:pharmacy app.jar

# 切换到应用用户
USER pharmacy

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-Dspring.profiles.active=prod", \
    "-Xms512m", \
    "-Xmx1024m", \
    "-XX:+UseG1GC", \
    "-XX:+UseStringDeduplication", \
    "-jar", \
    "app.jar"]
