# 药店管理系统前后端分离改造 - 部署指南

## 部署概述

本文档提供了药店管理系统前后端分离版本的完整部署指南，包括开发环境、测试环境和生产环境的部署方案。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue 3)   │    │  后端 (Spring)   │    │  数据库 (MySQL)  │
│   Port: 80/443  │────│   Port: 8080    │────│   Port: 3306    │
│   Nginx 代理     │    │   API 服务       │    │   数据存储       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境要求

### 服务器要求
- **操作系统**: CentOS 7+ / Ubuntu 18.04+ / macOS
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **硬盘**: 50GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Java**: JDK 8+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Nginx**: 1.18+
- **Maven**: 3.6+

## 部署步骤

### 1. 环境准备

#### 1.1 安装Java
```bash
# CentOS/RHEL
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-8-jdk

# 验证安装
java -version
javac -version
```

#### 1.2 安装Node.js
```bash
# 使用NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

#### 1.3 安装MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 1.4 安装Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE pharmacy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'pharmacy_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON pharmacy_db.* TO 'pharmacy_user'@'localhost';
FLUSH PRIVILEGES;

-- 设置max_allowed_packet
SET GLOBAL max_allowed_packet=268435456;
```

#### 2.2 导入数据
```bash
# 导入数据库结构和初始数据
mysql -u pharmacy_user -p pharmacy_db < database/pharmacy_db.sql
```

### 3. 后端部署

#### 3.1 构建后端应用
```bash
# 进入项目目录
cd /path/to/pharmacy

# 修改配置文件
cp src/main/resources/application-prod.properties.example src/main/resources/application-prod.properties

# 编辑生产环境配置
vim src/main/resources/application-prod.properties
```

**生产环境配置示例**:
```properties
# 数据库配置
spring.datasource.url=********************************************************************************************************************************************************************************
spring.datasource.username=pharmacy_user
spring.datasource.password=your_password

# JPA配置
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false

# 日志配置
logging.level.com.pharmacy.management=INFO
logging.file.name=/var/log/pharmacy/application.log

# JWT配置
jwt.secret=your_production_secret_key_here
jwt.access-token-expiration=86400000
jwt.refresh-token-expiration=604800000

# 文件上传配置
file.upload.path=/var/pharmacy/uploads
```

#### 3.2 打包应用
```bash
# 使用Maven打包
mvn clean package -Pprod

# 生成的JAR文件位于
ls target/pharmacy-management-1.0-SNAPSHOT.jar
```

#### 3.3 创建系统服务
```bash
# 创建应用目录
sudo mkdir -p /opt/pharmacy
sudo mkdir -p /var/log/pharmacy
sudo mkdir -p /var/pharmacy/uploads

# 复制JAR文件
sudo cp target/pharmacy-management-1.0-SNAPSHOT.jar /opt/pharmacy/

# 创建systemd服务文件
sudo vim /etc/systemd/system/pharmacy-backend.service
```

**服务配置文件**:
```ini
[Unit]
Description=Pharmacy Management Backend
After=network.target mysql.service

[Service]
Type=simple
User=pharmacy
Group=pharmacy
WorkingDirectory=/opt/pharmacy
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod pharmacy-management-1.0-SNAPSHOT.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 3.4 启动后端服务
```bash
# 创建用户
sudo useradd -r -s /bin/false pharmacy
sudo chown -R pharmacy:pharmacy /opt/pharmacy
sudo chown -R pharmacy:pharmacy /var/log/pharmacy
sudo chown -R pharmacy:pharmacy /var/pharmacy

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable pharmacy-backend
sudo systemctl start pharmacy-backend

# 检查状态
sudo systemctl status pharmacy-backend
```

### 4. 前端部署

#### 4.1 构建前端应用
```bash
# 进入前端目录
cd frontend/pharmacy-frontend

# 安装依赖
npm install

# 修改生产环境配置
vim .env.production
```

**生产环境配置**:
```env
# API基础URL
VITE_API_BASE_URL=https://your-domain.com/api

# 应用标题
VITE_APP_TITLE=药店管理系统

# 其他配置
VITE_APP_VERSION=1.0.0
```

#### 4.2 构建生产版本
```bash
# 构建生产版本
npm run build

# 生成的文件在dist目录
ls dist/
```

#### 4.3 部署到Nginx
```bash
# 创建网站目录
sudo mkdir -p /var/www/pharmacy

# 复制构建文件
sudo cp -r dist/* /var/www/pharmacy/

# 设置权限
sudo chown -R www-data:www-data /var/www/pharmacy
```

### 5. Nginx配置

#### 5.1 创建Nginx配置
```bash
sudo vim /etc/nginx/sites-available/pharmacy
```

**Nginx配置文件**:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 前端静态文件
    location / {
        root /var/www/pharmacy;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 文件上传
    location /uploads/ {
        alias /var/pharmacy/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 日志配置
    access_log /var/log/nginx/pharmacy_access.log;
    error_log /var/log/nginx/pharmacy_error.log;
}
```

#### 5.2 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/pharmacy /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 6. SSL证书配置

#### 6.1 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 监控配置

#### 7.1 日志监控
```bash
# 创建日志轮转配置
sudo vim /etc/logrotate.d/pharmacy
```

**日志轮转配置**:
```
/var/log/pharmacy/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 pharmacy pharmacy
    postrotate
        systemctl reload pharmacy-backend
    endscript
}
```

#### 7.2 系统监控脚本
```bash
# 创建监控脚本
sudo vim /opt/pharmacy/monitor.sh
```

**监控脚本**:
```bash
#!/bin/bash

# 检查后端服务
if ! systemctl is-active --quiet pharmacy-backend; then
    echo "$(date): Backend service is down, restarting..." >> /var/log/pharmacy/monitor.log
    systemctl restart pharmacy-backend
fi

# 检查Nginx服务
if ! systemctl is-active --quiet nginx; then
    echo "$(date): Nginx service is down, restarting..." >> /var/log/pharmacy/monitor.log
    systemctl restart nginx
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%, please check!" >> /var/log/pharmacy/monitor.log
fi
```

```bash
# 设置执行权限
sudo chmod +x /opt/pharmacy/monitor.sh

# 添加到crontab
sudo crontab -e
# 添加以下行
*/5 * * * * /opt/pharmacy/monitor.sh
```

## 部署验证

### 1. 服务状态检查
```bash
# 检查后端服务
sudo systemctl status pharmacy-backend

# 检查Nginx服务
sudo systemctl status nginx

# 检查MySQL服务
sudo systemctl status mysql
```

### 2. 功能测试
```bash
# 测试API接口
curl -X POST https://your-domain.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 测试前端访问
curl -I https://your-domain.com
```

### 3. 性能测试
```bash
# 使用ab进行简单压力测试
ab -n 1000 -c 10 https://your-domain.com/
```

## 故障排除

### 常见问题

1. **后端服务启动失败**
   - 检查Java版本和环境变量
   - 检查数据库连接配置
   - 查看日志文件 `/var/log/pharmacy/application.log`

2. **前端页面无法访问**
   - 检查Nginx配置语法
   - 检查文件权限
   - 查看Nginx错误日志

3. **API请求失败**
   - 检查Nginx代理配置
   - 检查后端服务状态
   - 检查防火墙设置

4. **数据库连接问题**
   - 检查MySQL服务状态
   - 检查用户权限
   - 检查网络连接

### 日志查看
```bash
# 后端应用日志
sudo tail -f /var/log/pharmacy/application.log

# Nginx访问日志
sudo tail -f /var/log/nginx/pharmacy_access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/pharmacy_error.log

# 系统日志
sudo journalctl -u pharmacy-backend -f
```

## 备份策略

### 数据库备份
```bash
# 创建备份脚本
sudo vim /opt/pharmacy/backup.sh
```

**备份脚本**:
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/pharmacy"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u pharmacy_user -p pharmacy_db > $BACKUP_DIR/pharmacy_db_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /var/pharmacy/uploads/

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# 设置定时备份
sudo crontab -e
# 添加以下行
0 2 * * * /opt/pharmacy/backup.sh
```

## 安全建议

1. **定期更新系统和软件包**
2. **使用强密码和密钥认证**
3. **配置防火墙规则**
4. **定期备份数据**
5. **监控系统日志**
6. **使用HTTPS加密传输**
7. **定期安全审计**

## 总结

本部署指南提供了完整的生产环境部署方案，包括环境准备、应用部署、服务配置、监控设置等各个方面。按照本指南操作，可以成功部署一个稳定、安全、高性能的药店管理系统。

如有问题，请参考故障排除部分或联系技术支持。
