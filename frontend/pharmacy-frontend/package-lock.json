{"name": "pharmacy-frontend", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "pharmacy-frontend", "version": "0.0.0", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@antfu/utils": {"version": "0.7.10", "resolved": "http://registry.m.jd.com/@antfu/utils/download/@antfu/utils-0.7.10.tgz", "integrity": "sha1-roKfFwFY4peptqKPFhqOSH0AgU0=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.2", "resolved": "http://registry.m.jd.com/@babel/compat-data/download/@babel/compat-data-7.27.2.tgz", "integrity": "sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/core/download/@babel/core-7.27.3.tgz", "integrity": "sha1-19BVArzO3jyrNjc+0ULmod9VTC8=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.3", "@babel/types": "^7.27.3", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "resolved": "http://registry.m.jd.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/generator/download/@babel/generator-7.27.3.tgz", "integrity": "sha1-7xwPfP47X8jLufbMafk0QaaO3vw=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.3", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.1.tgz", "integrity": "sha1-Q0XYGppGpkhuJNBpRp8T5gRFwF0=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "http://registry.m.jd.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "http://registry.m.jd.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz", "integrity": "sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "resolved": "http://registry.m.jd.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz", "integrity": "sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz", "integrity": "sha1-2wu8+6WAL573hwcFp++HiFCO3gI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz", "integrity": "sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz", "integrity": "sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "integrity": "sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/helpers/download/@babel/helpers-7.27.3.tgz", "integrity": "sha1-OH1l0nkpDiL+ekeo/80tDAGE7dA=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/parser/download/@babel/parser-7.27.3.tgz", "integrity": "sha1-G3Uz8NkIrSrFRcTQXL4vttyM+q8=", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.27.1.tgz", "integrity": "sha1-Nob0JLL4sv7nV5qk3xM6T1JEpZY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz", "integrity": "sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "http://registry.m.jd.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.27.1.tgz", "integrity": "sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.27.1.tgz", "integrity": "sha1-07tlWYvs4D93MRHojMTo5QcPEUA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "http://registry.m.jd.com/@babel/template/download/@babel/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/traverse/download/@babel/traverse-7.27.3.tgz", "integrity": "sha1-i2KmwtEPnZIbpzOckAdHCFCc/64=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/globals": {"version": "11.12.0", "resolved": "http://registry.m.jd.com/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/types": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/types/download/@babel/types-7.27.3.tgz", "integrity": "sha1-wCV77fM6rWqtH0BtNcRHWDIes+w=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "resolved": "http://registry.m.jd.com/@ctrl/tinycolor/download/@ctrl/tinycolor-3.6.1.tgz", "integrity": "sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@element-plus/icons-vue": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/@element-plus/icons-vue/download/@element-plus/icons-vue-2.3.1.tgz", "integrity": "sha1-H2Na1f3VyF7ZNkgVJVcOgrWoMHo=", "license": "MIT", "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.5.tgz", "integrity": "sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.5.tgz", "integrity": "sha1-QpDW00B7rjiDrSze0QgaI0RzziY=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.5.tgz", "integrity": "sha1-vHZkB/FxiSP2uAecjGG/hqw6ak8=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.5.tgz", "integrity": "sha1-QMEdnLyk8kBlSMipiV0yG8OzXv8=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.5.tgz", "integrity": "sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.5.tgz", "integrity": "sha1-4npdkqFIhu8dSS/VD8YaLU2H5Bg=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.5.tgz", "integrity": "sha1-l87eWdY4hAyhBOYFzbnxsRi6Cxw=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.5.tgz", "integrity": "sha1-ccd4EgQqGoGQw9WB4UDRW4drnG8=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.5.tgz", "integrity": "sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.5.tgz", "integrity": "sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.5.tgz", "integrity": "sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.5.tgz", "integrity": "sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.5.tgz", "integrity": "sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.5.tgz", "integrity": "sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.5.tgz", "integrity": "sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.5.tgz", "integrity": "sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.5.tgz", "integrity": "sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.5.tgz", "integrity": "sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.5.tgz", "integrity": "sha1-oCBvYxTOfchxO3cycD0PWN4dHnk=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.5.tgz", "integrity": "sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.5.tgz", "integrity": "sha1-KNDNiQm3+jlTr5mPKy7TT1dnKPA=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.5.tgz", "integrity": "sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.5.tgz", "integrity": "sha1-bq2+rTjovRL2M6UZDkXv+A4kAH4=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.5.tgz", "integrity": "sha1-urYogAVIL57Srbne1+iOuppizA0=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.5.tgz", "integrity": "sha1-f8EUr19lY/GfczJLXV/zbs4IA9E=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "http://registry.m.jd.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "http://registry.m.jd.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.0", "resolved": "http://registry.m.jd.com/@eslint/config-array/download/@eslint/config-array-0.20.0.tgz", "integrity": "sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-array/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.2", "resolved": "http://registry.m.jd.com/@eslint/config-helpers/download/@eslint/config-helpers-0.2.2.tgz", "integrity": "sha1-N3n3a4lN46jsR2O3lmDm1U1bEBA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.14.0", "resolved": "http://registry.m.jd.com/@eslint/core/download/@eslint/core-0.14.0.tgz", "integrity": "sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/@eslint/eslintrc/download/@eslint/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "9.27.0", "resolved": "http://registry.m.jd.com/@eslint/js/download/@eslint/js-9.27.0.tgz", "integrity": "sha1-GBojRgh3xIT23QOJD04/ov3rj/A=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "http://registry.m.jd.com/@eslint/object-schema/download/@eslint/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.1", "resolved": "http://registry.m.jd.com/@eslint/plugin-kit/download/@eslint/plugin-kit-0.3.1.tgz", "integrity": "sha1-txsDey1NaDlt8EqMNaSUgeVZMGc=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.14.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@floating-ui/core": {"version": "1.7.0", "resolved": "http://registry.m.jd.com/@floating-ui/core/download/@floating-ui/core-1.7.0.tgz", "integrity": "sha1-Gv8nqZPqGyVKWGMYwpw7FuoPTQo=", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/dom": {"version": "1.7.0", "resolved": "http://registry.m.jd.com/@floating-ui/dom/download/@floating-ui/dom-1.7.0.tgz", "integrity": "sha1-+fg+5P7nisI62eZbEo/BGieFdTI=", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.0", "@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/utils": {"version": "0.2.9", "resolved": "http://registry.m.jd.com/@floating-ui/utils/download/@floating-ui/utils-0.2.9.tgz", "integrity": "sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=", "license": "MIT"}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "http://registry.m.jd.com/@humanfs/core/download/@humanfs/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "http://registry.m.jd.com/@humanfs/node/download/@humanfs/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "resolved": "http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.4.3.tgz", "integrity": "sha1-wrnS43TuYsWG062+qHGZsdenpro=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "http://registry.m.jd.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "http://registry.m.jd.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "http://registry.m.jd.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://registry.m.jd.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@polka/url": {"version": "1.0.0-next.29", "resolved": "http://registry.m.jd.com/@polka/url/download/@polka/url-1.0.0-next.29.tgz", "integrity": "sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=", "dev": true, "license": "MIT"}, "node_modules/@popperjs/core": {"name": "@sxzz/popperjs-es", "version": "2.11.7", "resolved": "http://registry.m.jd.com/@sxzz/popperjs-es/download/@sxzz/popperjs-es-2.11.7.tgz", "integrity": "sha1-p/aeNmXT2psRX55xZx2uG5fhNnE=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@rollup/pluginutils": {"version": "5.1.4", "resolved": "http://registry.m.jd.com/@rollup/pluginutils/download/@rollup/pluginutils-5.1.4.tgz", "integrity": "sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/pluginutils/node_modules/picomatch": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.41.1.tgz", "integrity": "sha1-858J9g1KVi3nJ8lg17ICos95dCQ=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.41.1.tgz", "integrity": "sha1-0Zr34jdgcX8dh51Mo9LNJHdC3/I=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.41.1.tgz", "integrity": "sha1-HDovvyBdgGQXKOBfSlbJCelSGLc=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.41.1.tgz", "integrity": "sha1-qmbSuhol5glQDhO+8G3A5xzAwNQ=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.41.1.tgz", "integrity": "sha1-3xCntjFqDvECjGynGggRJMU34w0=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.41.1.tgz", "integrity": "sha1-o/3OigXpWwaMvLRuTfUYXkB9DDU=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.41.1.tgz", "integrity": "sha1-SfdmxVODvQSYAUqddpJDSMLziQw=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.41.1.tgz", "integrity": "sha1-HU19MvxVfhfVLhhXgXOB6jZeKVk=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.41.1.tgz", "integrity": "sha1-9PwxcmhEHpWJ7a076PYrbAMAm8E=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.41.1.tgz", "integrity": "sha1-Y6HxsGccsXgi2rroJ/7w5EOuvrc=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.41.1.tgz", "integrity": "sha1-xlmwHMbAcwtUdXH8OXPh6VU2n5g=", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.41.1.tgz", "integrity": "sha1-YS50b5rX5YSA+WTWXg1sP0quaag=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.41.1.tgz", "integrity": "sha1-RhDb0dz7uuMvvBDCCuc4essxEQw=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.41.1.tgz", "integrity": "sha1-BUkR+rQNyD+vwh5HAZPAWBCPGdg=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.41.1.tgz", "integrity": "sha1-mIluyoASVHx/BL0H6qaJaCX54aU=", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.41.1.tgz", "integrity": "sha1-Ac9WhEoeY27oDfs2TnLCtxQq2JY=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.41.1.tgz", "integrity": "sha1-5nx1Md9t/wtMJBEB1AlmF/vKh8M=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.41.1.tgz", "integrity": "sha1-furamEROWAZ03mmJKE5Lqs1I6mU=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.41.1.tgz", "integrity": "sha1-UWxLVPgFh7SjkKr0lAtAhwJx010=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.41.1.tgz", "integrity": "sha1-hI+ZsNmTbZIiG7YHC67/TbaUejA=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@sec-ant/readable-stream": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/@sec-ant/readable-stream/download/@sec-ant/readable-stream-0.4.1.tgz", "integrity": "sha1-YN6JG7Emq/3FQQ/cYWasoGXxCgw=", "dev": true, "license": "MIT"}, "node_modules/@sindresorhus/merge-streams": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/@sindresorhus/merge-streams/download/@sindresorhus/merge-streams-4.0.0.tgz", "integrity": "sha1-q7Edma620n8bVjw4FHpy1QBY4zk=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@tsconfig/node22": {"version": "22.0.1", "resolved": "http://registry.m.jd.com/@tsconfig/node22/download/@tsconfig/node22-22.0.1.tgz", "integrity": "sha1-J+PumzWeMeW5RpC/K61akjwdV9A=", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "http://registry.m.jd.com/@types/estree/download/@types/estree-1.0.7.tgz", "integrity": "sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "http://registry.m.jd.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.17", "resolved": "http://registry.m.jd.com/@types/lodash/download/@types/lodash-4.17.17.tgz", "integrity": "sha1-+4WgT0fp5NqIg4T+6tDeBfcHA1U=", "license": "MIT"}, "node_modules/@types/lodash-es": {"version": "4.17.12", "resolved": "http://registry.m.jd.com/@types/lodash-es/download/@types/lodash-es-4.17.12.tgz", "integrity": "sha1-ZfbR5fgFOap8+/yWLeXe8M9PNBs=", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/node": {"version": "22.15.23", "resolved": "http://registry.m.jd.com/@types/node/download/@types/node-22.15.23.tgz", "integrity": "sha1-oLfAP5UfH/44Gmo0XGjYDkgEPdA=", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/web-bluetooth": {"version": "0.0.16", "resolved": "http://registry.m.jd.com/@types/web-bluetooth/download/@types/web-bluetooth-0.0.16.tgz", "integrity": "sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg=", "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.33.0.tgz", "integrity": "sha1-Ue0DZJV1ulG87n79v9hSgySbVEc=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/type-utils": "8.33.0", "@typescript-eslint/utils": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.33.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.4", "resolved": "http://registry.m.jd.com/ignore/download/ignore-7.0.4.tgz", "integrity": "sha1-oSxw0PJgfFv1CPtlpAx18DfXoHg=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.33.0.tgz", "integrity": "sha1-jlI8K0R6181qyRtxnYs3RJSBeE0=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/typescript-estree": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/project-service/download/@typescript-eslint/project-service-8.33.0.tgz", "integrity": "sha1-cfN++QEN5HvyCWORR0PFy++FHgg=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.0", "@typescript-eslint/types": "^8.33.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.33.0.tgz", "integrity": "sha1-RZzwxJ1BCACxoCO5c8YtaZsJv0w=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/tsconfig-utils/download/@typescript-eslint/tsconfig-utils-8.33.0.tgz", "integrity": "sha1-MWrasDi73EPkSHgdWoFsKXPqtz4=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.33.0.tgz", "integrity": "sha1-8GEkstbbilGySZDLEjyVQ6+T/vU=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "8.33.0", "@typescript-eslint/utils": "8.33.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/types/download/@typescript-eslint/types-8.33.0.tgz", "integrity": "sha1-AqfbumEair8a0qngD3L3uUtasO4=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.33.0.tgz", "integrity": "sha1-q8wdPbdajp/S4nTujECZ+iOZq/0=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.33.0", "@typescript-eslint/tsconfig-utils": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/utils": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.33.0.tgz", "integrity": "sha1-V0rV7e43EHe54oym+4BPJED0R8E=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/typescript-estree": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.33.0.tgz", "integrity": "sha1-+64W/TWUUx+MrZXUIRJdY06ZdP4=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.33.0", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/visitor-keys/node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@vitejs/plugin-vue": {"version": "5.2.4", "resolved": "http://registry.m.jd.com/@vitejs/plugin-vue/download/@vitejs/plugin-vue-5.2.4.tgz", "integrity": "sha1-nopRLrF0v8KjM7qVm7+d5CjYmtg=", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}}, "node_modules/@volar/language-core": {"version": "2.4.14", "resolved": "http://registry.m.jd.com/@volar/language-core/download/@volar/language-core-2.4.14.tgz", "integrity": "sha1-2sdXMBTU87r7GGyxaIj/6laYvnE=", "dev": true, "license": "MIT", "dependencies": {"@volar/source-map": "2.4.14"}}, "node_modules/@volar/source-map": {"version": "2.4.14", "resolved": "http://registry.m.jd.com/@volar/source-map/download/@volar/source-map-2.4.14.tgz", "integrity": "sha1-zc7NUzwudnRJskFMwiMn0r2n75U=", "dev": true, "license": "MIT"}, "node_modules/@volar/typescript": {"version": "2.4.14", "resolved": "http://registry.m.jd.com/@volar/typescript/download/@volar/typescript-2.4.14.tgz", "integrity": "sha1-uZoQJd1qi3UelmJ+vLBznO7Q5fE=", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "2.4.14", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}}, "node_modules/@vue/babel-helper-vue-transform-on": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.4.0.tgz", "integrity": "sha1-YWAgSIaSqcQqYTKA1i7RtycEXZU=", "dev": true, "license": "MIT"}, "node_modules/@vue/babel-plugin-jsx": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.4.0.tgz", "integrity": "sha1-wVXHlc6YDt9Gqm/s7tk5Ralcplg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "@vue/babel-helper-vue-transform-on": "1.4.0", "@vue/babel-plugin-resolve-type": "1.4.0", "@vue/shared": "^3.5.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}}, "node_modules/@vue/babel-plugin-resolve-type": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/@vue/babel-plugin-resolve-type/download/@vue/babel-plugin-resolve-type-1.4.0.tgz", "integrity": "sha1-TTV6gfsMycrQ6MgbEYEVvaLFFUM=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.9", "@vue/compiler-sfc": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/sxzz"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/compiler-core": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/compiler-core/download/@vue/compiler-core-3.5.15.tgz", "integrity": "sha1-fL2mlClJD07A9oEm3Q1urgUN0Bw=", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/shared": "3.5.15", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/compiler-dom/download/@vue/compiler-dom-3.5.15.tgz", "integrity": "sha1-Lef+waaFwjZYWkofsSzFhtfw/+8=", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.15", "@vue/shared": "3.5.15"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.5.15.tgz", "integrity": "sha1-1FpcUXHYgjxZcl0xr0Bmk8iBYZU=", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/compiler-core": "3.5.15", "@vue/compiler-dom": "3.5.15", "@vue/compiler-ssr": "3.5.15", "@vue/shared": "3.5.15", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.3", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.5.15.tgz", "integrity": "sha1-tv+7Gf9xJvwOYMv5xMo02anOn7I=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.15", "@vue/shared": "3.5.15"}}, "node_modules/@vue/compiler-vue2": {"version": "2.7.16", "resolved": "http://registry.m.jd.com/@vue/compiler-vue2/download/@vue/compiler-vue2-2.7.16.tgz", "integrity": "sha1-K6g3y9PxszwryGX74aO1P7YR4kk=", "dev": true, "license": "MIT", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "node_modules/@vue/devtools-api": {"version": "7.7.6", "resolved": "http://registry.m.jd.com/@vue/devtools-api/download/@vue/devtools-api-7.7.6.tgz", "integrity": "sha1-SvXbx3vMhUPwqObwKfWY7ZeNbH0=", "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.6"}}, "node_modules/@vue/devtools-core": {"version": "7.7.6", "resolved": "http://registry.m.jd.com/@vue/devtools-core/download/@vue/devtools-core-7.7.6.tgz", "integrity": "sha1-fi75PAWvgJ5e0Vn/wbkQ8DCXa4M=", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "mitt": "^3.0.1", "nanoid": "^5.1.0", "pathe": "^2.0.3", "vite-hot-client": "^2.0.4"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@vue/devtools-core/node_modules/nanoid": {"version": "5.1.5", "resolved": "http://registry.m.jd.com/nanoid/download/nanoid-5.1.5.tgz", "integrity": "sha1-91l/nZBU602pVIzdU8pw8XkOh94=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.js"}, "engines": {"node": "^18 || >=20"}}, "node_modules/@vue/devtools-kit": {"version": "7.7.6", "resolved": "http://registry.m.jd.com/@vue/devtools-kit/download/@vue/devtools-kit-7.7.6.tgz", "integrity": "sha1-PZy+I3imXtfEuqd+zA9+zf0YX7s=", "license": "MIT", "dependencies": {"@vue/devtools-shared": "^7.7.6", "birpc": "^2.3.0", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.2"}}, "node_modules/@vue/devtools-shared": {"version": "7.7.6", "resolved": "http://registry.m.jd.com/@vue/devtools-shared/download/@vue/devtools-shared-7.7.6.tgz", "integrity": "sha1-XaIhjfYbYFt7iOclJB/GZA3w5LU=", "license": "MIT", "dependencies": {"rfdc": "^1.4.1"}}, "node_modules/@vue/eslint-config-typescript": {"version": "14.5.0", "resolved": "http://registry.m.jd.com/@vue/eslint-config-typescript/download/@vue/eslint-config-typescript-14.5.0.tgz", "integrity": "sha1-H9zp8q2PwRTTIJpKIAniYFomq7M=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^8.26.0", "fast-glob": "^3.3.3", "typescript-eslint": "^8.26.0", "vue-eslint-parser": "^10.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^9.10.0", "eslint-plugin-vue": "^9.28.0 || ^10.0.0", "typescript": ">=4.8.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/language-core": {"version": "2.2.10", "resolved": "http://registry.m.jd.com/@vue/language-core/download/@vue/language-core-2.2.10.tgz", "integrity": "sha1-WuHnGk4W3VnR5LrBZ/S5yMBNnxc=", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/reactivity": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/reactivity/download/@vue/reactivity-3.5.15.tgz", "integrity": "sha1-GNAK6SLosNwJr+lSvUdRDG5DBek=", "license": "MIT", "dependencies": {"@vue/shared": "3.5.15"}}, "node_modules/@vue/runtime-core": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/runtime-core/download/@vue/runtime-core-3.5.15.tgz", "integrity": "sha1-CsysS0QbtKoA9YujU7b2HoP0UgI=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.15", "@vue/shared": "3.5.15"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/runtime-dom/download/@vue/runtime-dom-3.5.15.tgz", "integrity": "sha1-BItyUGn20/5QAuU+uhGEZtH77IQ=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.15", "@vue/runtime-core": "3.5.15", "@vue/shared": "3.5.15", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/server-renderer/download/@vue/server-renderer-3.5.15.tgz", "integrity": "sha1-wehZfkvsjqCzI8MtwDKqXB8pg/Q=", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.15", "@vue/shared": "3.5.15"}, "peerDependencies": {"vue": "3.5.15"}}, "node_modules/@vue/shared": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/@vue/shared/download/@vue/shared-3.5.15.tgz", "integrity": "sha1-TGM6DmbzgRnjjuzzQLSmWwutcZI=", "license": "MIT"}, "node_modules/@vue/tsconfig": {"version": "0.7.0", "resolved": "http://registry.m.jd.com/@vue/tsconfig/download/@vue/tsconfig-0.7.0.tgz", "integrity": "sha1-ZwRMhHt6E3uMv9ayMQTDbbr4DR0=", "dev": true, "license": "MIT", "peerDependencies": {"typescript": "5.x", "vue": "^3.4.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vue": {"optional": true}}}, "node_modules/@vueuse/core": {"version": "9.13.0", "resolved": "http://registry.m.jd.com/@vueuse/core/download/@vueuse/core-9.13.0.tgz", "integrity": "sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "resolved": "http://registry.m.jd.com/vue-demi/download/vue-demi-0.14.10.tgz", "integrity": "sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "9.13.0", "resolved": "http://registry.m.jd.com/@vueuse/metadata/download/@vueuse/metadata-9.13.0.tgz", "integrity": "sha1-vCWmza0bGpPDbOMBkRJNplIFOf8=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "9.13.0", "resolved": "http://registry.m.jd.com/@vueuse/shared/download/@vueuse/shared-9.13.0.tgz", "integrity": "sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=", "license": "MIT", "dependencies": {"vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "resolved": "http://registry.m.jd.com/vue-demi/download/vue-demi-0.14.10.tgz", "integrity": "sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/acorn": {"version": "8.14.1", "resolved": "http://registry.m.jd.com/acorn/download/acorn-8.14.1.tgz", "integrity": "sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/alien-signals": {"version": "1.0.13", "resolved": "http://registry.m.jd.com/alien-signals/download/alien-signals-1.0.13.tgz", "integrity": "sha1-jW23NGL3Qu5riWcfvYw30LFyen4=", "dev": true, "license": "MIT"}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/async-validator": {"version": "4.2.5", "resolved": "http://registry.m.jd.com/async-validator/download/async-validator-4.2.5.tgz", "integrity": "sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://registry.m.jd.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/axios": {"version": "1.9.0", "resolved": "http://registry.m.jd.com/axios/download/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/birpc": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/birpc/download/birpc-2.3.0.tgz", "integrity": "sha1-5aQC3Hhe+VKiOD7zz8B14IQvPow=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/boolbase/download/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true, "license": "ISC"}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.1.tgz", "integrity": "sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.5", "resolved": "http://registry.m.jd.com/browserslist/download/browserslist-4.24.5.tgz", "integrity": "sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001716", "electron-to-chromium": "^1.5.149", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bundle-name": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/bundle-name/download/bundle-name-4.1.0.tgz", "integrity": "sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=", "dev": true, "license": "MIT", "dependencies": {"run-applescript": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001718", "resolved": "http://registry.m.jd.com/caniuse-lite/download/caniuse-lite-1.0.30001718.tgz", "integrity": "sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "http://registry.m.jd.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/convert-source-map/download/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/copy-anything": {"version": "3.0.5", "resolved": "http://registry.m.jd.com/copy-anything/download/copy-anything-3.0.5.tgz", "integrity": "sha1-LZLc6MSY95D6etFrAaGuWkWwIKA=", "license": "MIT", "dependencies": {"is-what": "^4.1.8"}, "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "http://registry.m.jd.com/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/cssesc/download/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/csstype/download/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "http://registry.m.jd.com/dayjs/download/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw=", "license": "MIT"}, "node_modules/de-indent": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/de-indent/download/de-indent-1.0.2.tgz", "integrity": "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/default-browser": {"version": "5.2.1", "resolved": "http://registry.m.jd.com/default-browser/download/default-browser-5.2.1.tgz", "integrity": "sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=", "dev": true, "license": "MIT", "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/default-browser-id/download/default-browser-id-5.0.0.tgz", "integrity": "sha1-odmL+WDBUILYo/pp6DFQzMzDryY=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/define-lazy-prop/download/define-lazy-prop-3.0.0.tgz", "integrity": "sha1-27Ga37dG1/xtc0oGty9KANAhJV8=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/echarts": {"version": "5.6.0", "resolved": "http://registry.m.jd.com/echarts/download/echarts-5.6.0.tgz", "integrity": "sha1-I3eHTcqftQ8QQFHDVTVEdS2jydY=", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/electron-to-chromium": {"version": "1.5.158", "resolved": "http://registry.m.jd.com/electron-to-chromium/download/electron-to-chromium-1.5.158.tgz", "integrity": "sha1-5fAfx/34ENnSI+MFk+CDnDBidtQ=", "dev": true, "license": "ISC"}, "node_modules/element-plus": {"version": "2.9.11", "resolved": "http://registry.m.jd.com/element-plus/download/element-plus-2.9.11.tgz", "integrity": "sha1-yTmo2UUzD1lrejWq4OUB6hcIdKI=", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/entities": {"version": "4.5.0", "resolved": "http://registry.m.jd.com/entities/download/entities-4.5.0.tgz", "integrity": "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-stack-parser-es": {"version": "0.1.5", "resolved": "http://registry.m.jd.com/error-stack-parser-es/download/error-stack-parser-es-0.1.5.tgz", "integrity": "sha1-FbULZ76ktu1llpdu4Hx4Z64luxw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.25.5", "resolved": "http://registry.m.jd.com/esbuild/download/esbuild-0.25.5.tgz", "integrity": "sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/escalade/download/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://registry.m.jd.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.27.0", "resolved": "http://registry.m.jd.com/eslint/download/eslint-9.27.0.tgz", "integrity": "sha1-pYfTzVuES2jfeJiUQyOnAq/jiXk=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.27.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-plugin-vue": {"version": "10.0.1", "resolved": "http://registry.m.jd.com/eslint-plugin-vue/download/eslint-plugin-vue-10.0.1.tgz", "integrity": "sha1-UZQxjrdvmMz3t9KPDHEDhCx/SA8=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "vue-eslint-parser": "^10.0.0"}}, "node_modules/eslint-scope": {"version": "8.3.0", "resolved": "http://registry.m.jd.com/eslint-scope/download/eslint-scope-8.3.0.tgz", "integrity": "sha1-EM06kY/91yL18/e1uD25sjyHNA0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/espree": {"version": "10.3.0", "resolved": "http://registry.m.jd.com/espree/download/espree-10.3.0.tgz", "integrity": "sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree/node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "http://registry.m.jd.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "http://registry.m.jd.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/estree-walker/download/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/execa": {"version": "9.6.0", "resolved": "http://registry.m.jd.com/execa/download/execa-9.6.0.tgz", "integrity": "sha1-OGZVMOVOLgGDhBCDIvN/Na5087w=", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/merge-streams": "^4.0.0", "cross-spawn": "^7.0.6", "figures": "^6.1.0", "get-stream": "^9.0.0", "human-signals": "^8.0.1", "is-plain-obj": "^4.1.0", "is-stream": "^4.0.1", "npm-run-path": "^6.0.0", "pretty-ms": "^9.2.0", "signal-exit": "^4.1.0", "strip-final-newline": "^4.0.0", "yoctocolors": "^2.1.1"}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/fast-glob/download/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "http://registry.m.jd.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "http://registry.m.jd.com/fastq/download/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/figures": {"version": "6.1.0", "resolved": "http://registry.m.jd.com/figures/download/figures-6.1.0.tgz", "integrity": "sha1-k1R59Rhl+nR59vqU/G/HrBTmLEo=", "dev": true, "license": "MIT", "dependencies": {"is-unicode-supported": "^2.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/find-up/download/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/flat-cache/download/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "http://registry.m.jd.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/form-data/download/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-extra": {"version": "11.3.0", "resolved": "http://registry.m.jd.com/fs-extra/download/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "http://registry.m.jd.com/fsevents/download/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "http://registry.m.jd.com/gensync/download/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "9.0.1", "resolved": "http://registry.m.jd.com/get-stream/download/get-stream-9.0.1.tgz", "integrity": "sha1-lRV9Id+OuQ0WRxArYwObHfYOvSc=", "dev": true, "license": "MIT", "dependencies": {"@sec-ant/readable-stream": "^0.4.1", "is-stream": "^4.0.1"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "14.0.0", "resolved": "http://registry.m.jd.com/globals/download/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://registry.m.jd.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/graphemer/download/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/he/download/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hookable": {"version": "5.5.3", "resolved": "http://registry.m.jd.com/hookable/download/hookable-5.5.3.tgz", "integrity": "sha1-bPw1iYSh75keJRjLntSneLvTIV0=", "license": "MIT"}, "node_modules/human-signals": {"version": "8.0.1", "resolved": "http://registry.m.jd.com/human-signals/download/human-signals-8.0.1.tgz", "integrity": "sha1-8Iu1k7bR2zU5M9BhVs7eyQq+Ufs=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/is-docker": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/is-docker/download/is-docker-3.0.0.tgz", "integrity": "sha1-kAk6oxBid9inelkQ265xdH4VogA=", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/is-inside-container/download/is-inside-container-1.0.0.tgz", "integrity": "sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-obj": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/is-plain-obj/download/is-plain-obj-4.1.0.tgz", "integrity": "sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-stream": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/is-stream/download/is-stream-4.0.1.tgz", "integrity": "sha1-N1z4keFtLkuuwlC4WSbP/BRyDZs=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-unicode-supported": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/is-unicode-supported/download/is-unicode-supported-2.1.0.tgz", "integrity": "sha1-CfCrDebTdE1I0mXruY9l0R8qmzo=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-what": {"version": "4.1.16", "resolved": "http://registry.m.jd.com/is-what/download/is-what-4.1.16.tgz", "integrity": "sha1-GthgoZ2otIla1Uldoxgs4qzdem8=", "license": "MIT", "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/is-wsl": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/is-wsl/download/is-wsl-3.1.0.tgz", "integrity": "sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=", "dev": true, "license": "MIT", "dependencies": {"is-inside-container": "^1.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/jiti": {"version": "2.4.2", "resolved": "http://registry.m.jd.com/jiti/download/jiti-2.4.2.tgz", "integrity": "sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=", "dev": true, "license": "MIT", "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/jsesc/download/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/json-buffer/download/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/json-parse-even-better-errors/download/json-parse-even-better-errors-4.0.0.tgz", "integrity": "sha1-0/Z71ZJegdPjGqRmrMghyDdc7EM=", "dev": true, "license": "MIT", "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://registry.m.jd.com/json5/download/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "http://registry.m.jd.com/jsonfile/download/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "http://registry.m.jd.com/keyv/download/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kolorist": {"version": "1.8.0", "resolved": "http://registry.m.jd.com/kolorist/download/kolorist-1.8.0.tgz", "integrity": "sha1-7d27vHiUvBMwLN90CvY3TUoEdDw=", "dev": true, "license": "MIT"}, "node_modules/levn": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/levn/download/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/locate-path/download/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://registry.m.jd.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "http://registry.m.jd.com/lodash-es/download/lodash-es-4.17.21.tgz", "integrity": "sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=", "license": "MIT"}, "node_modules/lodash-unified": {"version": "1.0.3", "resolved": "http://registry.m.jd.com/lodash-unified/download/lodash-unified-1.0.3.tgz", "integrity": "sha1-gLHqwQ7S6wLtGJ8IYUopwn0HyJQ=", "license": "MIT", "peerDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "http://registry.m.jd.com/lodash.merge/download/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://registry.m.jd.com/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "http://registry.m.jd.com/magic-string/download/magic-string-0.30.17.tgz", "integrity": "sha1-RQpElnPSRg5bvPupphkWoXFMdFM=", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/memoize-one": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/memoize-one/download/memoize-one-6.0.0.tgz", "integrity": "sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=", "license": "MIT"}, "node_modules/memorystream": {"version": "0.3.1", "resolved": "http://registry.m.jd.com/memorystream/download/memorystream-0.3.1.tgz", "integrity": "sha1-htcJCzDORV1j+64S3aUaR93K+bI=", "dev": true, "engines": {"node": ">= 0.10.0"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/merge2/download/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://registry.m.jd.com/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "http://registry.m.jd.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "http://registry.m.jd.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "9.0.5", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mitt": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/mitt/download/mitt-3.0.1.tgz", "integrity": "sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=", "license": "MIT"}, "node_modules/mrmime": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/mrmime/download/mrmime-2.0.1.tgz", "integrity": "sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://registry.m.jd.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "dev": true, "license": "MIT"}, "node_modules/muggle-string": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/muggle-string/download/muggle-string-0.4.1.tgz", "integrity": "sha1-OzZr1Dsy+AncIGWVNN0w58ig0yg=", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "http://registry.m.jd.com/nanoid/download/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "http://registry.m.jd.com/node-releases/download/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-wheel-es": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/normalize-wheel-es/download/normalize-wheel-es-1.2.0.tgz", "integrity": "sha1-D6JZPWGfckWlQWUmGRBasHas8J4=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npm-normalize-package-bin": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/npm-normalize-package-bin/download/npm-normalize-package-bin-4.0.0.tgz", "integrity": "sha1-33nnDNChE7d8AtH+JDyWuOYYrLE=", "dev": true, "license": "ISC", "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/npm-run-all2": {"version": "7.0.2", "resolved": "http://registry.m.jd.com/npm-run-all2/download/npm-run-all2-7.0.2.tgz", "integrity": "sha1-JhVcFAtePxFV79f11nISyAJ7OXw=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "cross-spawn": "^7.0.6", "memorystream": "^0.3.1", "minimatch": "^9.0.0", "pidtree": "^0.6.0", "read-package-json-fast": "^4.0.0", "shell-quote": "^1.7.3", "which": "^5.0.0"}, "bin": {"npm-run-all": "bin/npm-run-all/index.js", "npm-run-all2": "bin/npm-run-all/index.js", "run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js"}, "engines": {"node": "^18.17.0 || >=20.5.0", "npm": ">= 9"}}, "node_modules/npm-run-all2/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/npm-run-all2/node_modules/isexe": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/isexe/download/isexe-3.1.1.tgz", "integrity": "sha1-SkB+K9eN37FL6gwnxvcHLd53Xw0=", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/npm-run-all2/node_modules/which": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/which/download/which-5.0.0.tgz", "integrity": "sha1-2T8tk/eYNNQ2PH0MI+ANB8RmyNY=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/npm-run-path": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/npm-run-path/download/npm-run-path-6.0.0.tgz", "integrity": "sha1-Jc/cTq4El28zScCxr8CJBSw2JTc=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0", "unicorn-magic": "^0.3.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/path-key/download/path-key-4.0.0.tgz", "integrity": "sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/nth-check": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/nth-check/download/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/open": {"version": "10.1.2", "resolved": "http://registry.m.jd.com/open/download/open-10.1.2.tgz", "integrity": "sha1-1d9AmEdVyanDyT34FWoSRn6IKSU=", "dev": true, "license": "MIT", "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "http://registry.m.jd.com/optionator/download/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/p-locate/download/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-ms": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/parse-ms/download/parse-ms-4.0.0.tgz", "integrity": "sha1-wMBY7dR8KlkBUacYmQUz/WKAPfQ=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-browserify": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/path-browserify/download/path-browserify-1.0.1.tgz", "integrity": "sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathe": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/pathe/download/pathe-2.0.3.tgz", "integrity": "sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=", "dev": true, "license": "MIT"}, "node_modules/perfect-debounce": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/perfect-debounce/download/perfect-debounce-1.0.0.tgz", "integrity": "sha1-nC6LwwsWnMmEpYt9WygEmDlZHSo=", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "resolved": "http://registry.m.jd.com/pidtree/download/pidtree-0.6.0.tgz", "integrity": "sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pinia": {"version": "3.0.2", "resolved": "http://registry.m.jd.com/pinia/download/pinia-3.0.2.tgz", "integrity": "sha1-BhbC4bOZFfJTx2Jts8gbfNrWldo=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^7.7.2"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"typescript": ">=4.4.4", "vue": "^2.7.0 || ^3.5.11"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/postcss": {"version": "8.5.3", "resolved": "http://registry.m.jd.com/postcss/download/postcss-8.5.3.tgz", "integrity": "sha1-FGO28cf7Fv4lhzbLopot41I36vs=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "resolved": "http://registry.m.jd.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz", "integrity": "sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/prelude-ls/download/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-ms": {"version": "9.2.0", "resolved": "http://registry.m.jd.com/pretty-ms/download/pretty-ms-9.2.0.tgz", "integrity": "sha1-4UwKrWSTtp7WMRREKoQTPX5WDvA=", "dev": true, "license": "MIT", "dependencies": {"parse-ms": "^4.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "http://registry.m.jd.com/queue-microtask/download/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/read-package-json-fast": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/read-package-json-fast/download/read-package-json-fast-4.0.0.tgz", "integrity": "sha1-jMvAV0C7n1gmT0AKzAtLTu6NGzk=", "dev": true, "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^4.0.0", "npm-normalize-package-bin": "^4.0.0"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/reusify/download/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/rfdc/download/rfdc-1.4.1.tgz", "integrity": "sha1-d492xPtzHZNBTo+SX77PZMzn9so=", "license": "MIT"}, "node_modules/rollup": {"version": "4.41.1", "resolved": "http://registry.m.jd.com/rollup/download/rollup-4.41.1.tgz", "integrity": "sha1-Rt3BszzxsLqpkyDTsLSXPcIlO2o=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.41.1", "@rollup/rollup-android-arm64": "4.41.1", "@rollup/rollup-darwin-arm64": "4.41.1", "@rollup/rollup-darwin-x64": "4.41.1", "@rollup/rollup-freebsd-arm64": "4.41.1", "@rollup/rollup-freebsd-x64": "4.41.1", "@rollup/rollup-linux-arm-gnueabihf": "4.41.1", "@rollup/rollup-linux-arm-musleabihf": "4.41.1", "@rollup/rollup-linux-arm64-gnu": "4.41.1", "@rollup/rollup-linux-arm64-musl": "4.41.1", "@rollup/rollup-linux-loongarch64-gnu": "4.41.1", "@rollup/rollup-linux-powerpc64le-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-musl": "4.41.1", "@rollup/rollup-linux-s390x-gnu": "4.41.1", "@rollup/rollup-linux-x64-gnu": "4.41.1", "@rollup/rollup-linux-x64-musl": "4.41.1", "@rollup/rollup-win32-arm64-msvc": "4.41.1", "@rollup/rollup-win32-ia32-msvc": "4.41.1", "@rollup/rollup-win32-x64-msvc": "4.41.1", "fsevents": "~2.3.2"}}, "node_modules/run-applescript": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/run-applescript/download/run-applescript-7.0.0.tgz", "integrity": "sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/run-parallel/download/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/semver": {"version": "7.7.2", "resolved": "http://registry.m.jd.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.2", "resolved": "http://registry.m.jd.com/shell-quote/download/shell-quote-1.8.2.tgz", "integrity": "sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/signal-exit/download/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/sirv": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/sirv/download/sirv-3.0.1.tgz", "integrity": "sha1-MqhEeUZVtyf54oZ7d34AYPvge/M=", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">=18"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/source-map-js/download/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/speakingurl": {"version": "14.0.1", "resolved": "http://registry.m.jd.com/speakingurl/download/speakingurl-14.0.1.tgz", "integrity": "sha1-837I3cSrmOlgDByewySoxI13KlM=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-final-newline": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/strip-final-newline/download/strip-final-newline-4.0.0.tgz", "integrity": "sha1-NaNp7CrEPfNW4+3V3Ou2Qpqh+lw=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/superjson": {"version": "2.2.2", "resolved": "http://registry.m.jd.com/superjson/download/superjson-2.2.2.tgz", "integrity": "sha1-nVK/C/a1dRo8NHLxKS5xR4K6MXM=", "license": "MIT", "dependencies": {"copy-anything": "^3.0.2"}, "engines": {"node": ">=16"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "http://registry.m.jd.com/tinyglobby/download/tinyglobby-0.2.14.tgz", "integrity": "sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.4", "resolved": "http://registry.m.jd.com/fdir/download/fdir-6.4.4.tgz", "integrity": "sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/totalist": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/totalist/download/totalist-3.0.1.tgz", "integrity": "sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz", "integrity": "sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/tslib": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/tslib/download/tslib-2.3.0.tgz", "integrity": "sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "http://registry.m.jd.com/type-check/download/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "http://registry.m.jd.com/typescript/download/typescript-5.8.3.tgz", "integrity": "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.33.0", "resolved": "http://registry.m.jd.com/typescript-eslint/download/typescript-eslint-8.33.0.tgz", "integrity": "sha1-ifczqQ7car4JlLYTC5ZOeBobqC8=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "8.33.0", "@typescript-eslint/parser": "8.33.0", "@typescript-eslint/utils": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "dev": true, "license": "MIT"}, "node_modules/unicorn-magic": {"version": "0.3.0", "resolved": "http://registry.m.jd.com/unicorn-magic/download/unicorn-magic-0.3.0.tgz", "integrity": "sha1-Tv1FyFpp4N1XbSVTL7+iKqXIoQQ=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/universalify": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/universalify/download/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "license": "MIT"}, "node_modules/vite": {"version": "6.3.5", "resolved": "http://registry.m.jd.com/vite/download/vite-6.3.5.tgz", "integrity": "sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-hot-client": {"version": "2.0.4", "resolved": "http://registry.m.jd.com/vite-hot-client/download/vite-hot-client-2.0.4.tgz", "integrity": "sha1-2zg+AzfHWPur8U2tJvmhvLnp4XU=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-plugin-inspect": {"version": "0.8.9", "resolved": "http://registry.m.jd.com/vite-plugin-inspect/download/vite-plugin-inspect-0.8.9.tgz", "integrity": "sha1-AafkhMy8EqjIbui8kO/hOusP7Rs=", "dev": true, "license": "MIT", "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.3", "debug": "^4.3.7", "error-stack-parser-es": "^0.1.5", "fs-extra": "^11.2.0", "open": "^10.1.0", "perfect-debounce": "^1.0.0", "picocolors": "^1.1.1", "sirv": "^3.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.1"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}}}, "node_modules/vite-plugin-vue-devtools": {"version": "7.7.6", "resolved": "http://registry.m.jd.com/vite-plugin-vue-devtools/download/vite-plugin-vue-devtools-7.7.6.tgz", "integrity": "sha1-125hotU+iOcqranT+JUps6z9sa4=", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-core": "^7.7.6", "@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "execa": "^9.5.2", "sirv": "^3.0.1", "vite-plugin-inspect": "0.8.9", "vite-plugin-vue-inspector": "^5.3.1"}, "engines": {"node": ">=v14.21.3"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-plugin-vue-inspector": {"version": "5.3.1", "resolved": "http://registry.m.jd.com/vite-plugin-vue-inspector/download/vite-plugin-vue-inspector-5.3.1.tgz", "integrity": "sha1-5Xq9sRsV3qD12wsK8uLQsjbBhxc=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-decorators": "^7.23.0", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.22.15", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/compiler-dom": "^3.3.4", "kolorist": "^1.8.0", "magic-string": "^0.30.4"}, "peerDependencies": {"vite": "^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.4", "resolved": "http://registry.m.jd.com/fdir/download/fdir-6.4.4.tgz", "integrity": "sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/vscode-uri": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/vscode-uri/download/vscode-uri-3.1.0.tgz", "integrity": "sha1-3QnsWmaji1w//8d0AVcTSW0U4Jw=", "dev": true, "license": "MIT"}, "node_modules/vue": {"version": "3.5.15", "resolved": "http://registry.m.jd.com/vue/download/vue-3.5.15.tgz", "integrity": "sha1-WJZWmjOhvK/XZMayfk5vjLVfS+4=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.15", "@vue/compiler-sfc": "3.5.15", "@vue/runtime-dom": "3.5.15", "@vue/server-renderer": "3.5.15", "@vue/shared": "3.5.15"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-demi": {"version": "0.13.11", "resolved": "http://registry.m.jd.com/vue-demi/download/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/vue-echarts": {"version": "7.0.3", "resolved": "http://registry.m.jd.com/vue-echarts/download/vue-echarts-7.0.3.tgz", "integrity": "sha1-v3n37gFEu9xq7lYQ6EQ/7ZH2q74=", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/runtime-core": "^3.0.0", "echarts": "^5.5.1", "vue": "^2.7.0 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/runtime-core": {"optional": true}}}, "node_modules/vue-eslint-parser": {"version": "10.1.3", "resolved": "http://registry.m.jd.com/vue-eslint-parser/download/vue-eslint-parser-10.1.3.tgz", "integrity": "sha1-lkV4I6WRWmIAF5jP2cwVqJBnv4E=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}}, "node_modules/vue-eslint-parser/node_modules/eslint-visitor-keys": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz", "integrity": "sha1-aHussq+IT83aim59ZcYG9GoUzUU=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-router": {"version": "4.5.1", "resolved": "http://registry.m.jd.com/vue-router/download/vue-router-4.5.1.tgz", "integrity": "sha1-R7/+LTpUedKIapokRUeoU6oKv2k=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue-router/node_modules/@vue/devtools-api": {"version": "6.6.4", "resolved": "http://registry.m.jd.com/@vue/devtools-api/download/@vue/devtools-api-6.6.4.tgz", "integrity": "sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=", "license": "MIT"}, "node_modules/vue-tsc": {"version": "2.2.10", "resolved": "http://registry.m.jd.com/vue-tsc/download/vue-tsc-2.2.10.tgz", "integrity": "sha1-e1GmZsuQeIiE79DK7cafwfycW3g=", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.10"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "peerDependencies": {"typescript": ">=5.0.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "http://registry.m.jd.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/xml-name-validator": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/xml-name-validator/download/xml-name-validator-4.0.0.tgz", "integrity": "sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "http://registry.m.jd.com/yocto-queue/download/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yoctocolors": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/yoctocolors/download/yoctocolors-2.1.1.tgz", "integrity": "sha1-4BZ0dOn7ueiz7MpzjeqmHdEuVvw=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zrender": {"version": "5.6.1", "resolved": "http://registry.m.jd.com/zrender/download/zrender-5.6.1.tgz", "integrity": "sha1-4I1X7PSsrHCMT8t0gesgHffxCms=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "2.3.0"}}}}