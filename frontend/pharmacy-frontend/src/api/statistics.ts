import request from '@/utils/request'

// 统计相关接口
export interface StatisticsParams {
  startDate: string
  endDate: string
  pharmacyId?: number
}

export interface TrendParams extends StatisticsParams {
  groupBy?: 'day' | 'week' | 'month'
}

export interface BasicStatistics {
  newPatients: number
  visitedPatients: number
  infusionCount: number
  oralCount: number
  infusionAmount: number
  oralAmount: number
  totalAmount: number
}

export interface GenderDistribution {
  male: number
  female: number
  unknown: number
}

export interface AgeDistribution {
  [key: string]: number
}

export interface TreatmentDistribution {
  infusion: number
  oral: number
  both: number
}

export interface TrendData {
  date: string
  newPatients: number
  visitedPatients: number
  infusionCount: number
  oralCount: number
  totalAmount: number
}

export interface ComprehensiveStatistics {
  basic: BasicStatistics
  gender: GenderDistribution
  age: AgeDistribution
  treatment: TreatmentDistribution
  trend: TrendData[]
}

// 统计API服务
export const statisticsApi = {
  // 获取基本统计数据
  getBasicStatistics: (params: StatisticsParams) => 
    request.get<BasicStatistics>('/statistics/basic', { params }),

  // 获取性别分布
  getGenderDistribution: (params: StatisticsParams) => 
    request.get<GenderDistribution>('/statistics/gender', { params }),

  // 获取年龄分布
  getAgeDistribution: (params: StatisticsParams) => 
    request.get<AgeDistribution>('/statistics/age', { params }),

  // 获取治疗方式分布
  getTreatmentDistribution: (params: StatisticsParams) => 
    request.get<TreatmentDistribution>('/statistics/treatment', { params }),

  // 获取趋势数据
  getTrendData: (params: TrendParams) => 
    request.get<TrendData[]>('/statistics/trend', { params }),

  // 获取综合统计数据
  getComprehensiveStatistics: (params: StatisticsParams) => 
    request.get<ComprehensiveStatistics>('/statistics/comprehensive', { params }),

  // 导出统计数据
  exportStatistics: (params: StatisticsParams) => 
    request.get('/statistics/export', { 
      params, 
      responseType: 'blob' 
    }),

  // 获取用户可访问的药店列表
  getUserPharmacies: () => 
    request.get<any[]>('/statistics/pharmacies')
}
