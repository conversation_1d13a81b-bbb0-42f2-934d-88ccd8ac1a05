<template>
  <div class="pharmacy-select-container">
    <div class="select-card">
      <div class="select-header">
        <h2>请选择药店</h2>
        <p>选择您要管理的药店</p>
      </div>
      
      <div class="pharmacy-grid">
        <div 
          v-for="pharmacy in userPharmacies" 
          :key="pharmacy.id"
          class="pharmacy-card"
          :class="{ 'inactive': !pharmacy.isActive }"
          @click="selectPharmacy(pharmacy)"
        >
          <div class="pharmacy-header">
            <h3>{{ pharmacy.name }}</h3>
            <el-tag 
              :type="pharmacy.isActive ? 'success' : 'danger'"
              size="small"
            >
              {{ pharmacy.isActive ? '正常营业' : '已停业' }}
            </el-tag>
          </div>
          
          <div class="pharmacy-info">
            <p><strong>地址：</strong>{{ pharmacy.address }}</p>
            <p><strong>电话：</strong>{{ pharmacy.phone }}</p>
            <p v-if="pharmacy.businessHours">
              <strong>营业时间：</strong>{{ pharmacy.businessHours }}
            </p>
            <p v-if="pharmacy.description">
              <strong>简介：</strong>{{ pharmacy.description }}
            </p>
          </div>
          
          <div class="pharmacy-actions">
            <el-button 
              type="primary" 
              :disabled="!pharmacy.isActive"
              @click.stop="selectPharmacy(pharmacy)"
            >
              {{ pharmacy.isActive ? '选择此药店' : '药店已停业' }}
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="userPharmacies.length === 0" class="no-pharmacy">
        <el-empty description="您还没有关联任何药店">
          <el-button type="primary" @click="contactAdmin">
            联系管理员
          </el-button>
        </el-empty>
      </div>
      
      <div class="select-footer">
        <el-button @click="logout">退出登录</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { usePharmacyStore } from '@/stores/pharmacy'
import type { Pharmacy } from '@/types/api'

const router = useRouter()
const userStore = useUserStore()
const pharmacyStore = usePharmacyStore()

const userPharmacies = ref<Pharmacy[]>([])

// 获取用户药店列表
const fetchPharmacies = async () => {
  try {
    await pharmacyStore.fetchUserPharmacies()
    userPharmacies.value = pharmacyStore.userPharmacies
  } catch (error) {
    console.error('Failed to fetch pharmacies:', error)
    ElMessage.error('获取药店列表失败')
  }
}

// 选择药店
const selectPharmacy = async (pharmacy: Pharmacy) => {
  if (!pharmacy.isActive) {
    ElMessage.warning('无法选择已停业的药店')
    return
  }
  
  try {
    await pharmacyStore.switchPharmacy(pharmacy.id)
    ElMessage.success(`已选择药店：${pharmacy.name}`)
    
    // 跳转到主界面
    router.push('/dashboard')
  } catch (error) {
    console.error('Failed to select pharmacy:', error)
    ElMessage.error('选择药店失败')
  }
}

// 联系管理员
const contactAdmin = () => {
  ElMessageBox.alert(
    '请联系系统管理员为您分配药店权限。',
    '提示',
    {
      confirmButtonText: '确定'
    }
  )
}

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    pharmacyStore.clearPharmacyState()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    // 用户取消操作
  }
}

onMounted(() => {
  fetchPharmacies()
})
</script>

<style scoped>
.pharmacy-select-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.select-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.select-header {
  text-align: center;
  margin-bottom: 40px;
}

.select-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.select-header p {
  color: #666;
  font-size: 16px;
}

.pharmacy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.pharmacy-card {
  border: 2px solid #e6e6e6;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.pharmacy-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.pharmacy-card.inactive {
  opacity: 0.6;
  cursor: not-allowed;
}

.pharmacy-card.inactive:hover {
  border-color: #e6e6e6;
  box-shadow: none;
  transform: none;
}

.pharmacy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.pharmacy-header h3 {
  color: #333;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.pharmacy-info {
  margin-bottom: 20px;
}

.pharmacy-info p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.pharmacy-actions {
  text-align: center;
}

.no-pharmacy {
  text-align: center;
  padding: 40px;
}

.select-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

@media (max-width: 768px) {
  .select-card {
    padding: 20px;
    margin: 10px;
  }
  
  .pharmacy-grid {
    grid-template-columns: 1fr;
  }
  
  .select-header h2 {
    font-size: 24px;
  }
}
</style>
