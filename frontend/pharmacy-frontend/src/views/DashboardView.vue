<template>
  <div class="page-container">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-left">
        <h1>药店管理系统</h1>
      </div>
      
      <div class="header-right">
        <!-- 药店切换 -->
        <el-select
          v-if="pharmacyStore.hasPharmacies"
          v-model="currentPharmacyId"
          placeholder="选择药店"
          class="pharmacy-selector"
          @change="handlePharmacyChange"
        >
          <el-option
            v-for="pharmacy in pharmacyStore.userPharmacies"
            :key="pharmacy.id"
            :label="pharmacy.name"
            :value="pharmacy.id"
          />
        </el-select>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <span class="user-dropdown">
            <el-avatar :size="32" :src="userAvatar">
              {{ userStore.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="username">{{ userStore.user?.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边导航 -->
      <el-aside width="200px" class="sidebar">
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          
          <el-menu-item index="/patients">
            <el-icon><User /></el-icon>
            <span>患者管理</span>
          </el-menu-item>
          
          <el-menu-item index="/medical-records">
            <el-icon><Document /></el-icon>
            <span>就诊记录</span>
          </el-menu-item>
          
          <el-menu-item index="/quick-medicines">
            <el-icon><Medicine /></el-icon>
            <span>快捷药品</span>
          </el-menu-item>
          
          <el-menu-item index="/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <span>统计报表</span>
          </el-menu-item>
          
          <el-menu-item v-if="isAdmin" index="/pharmacies">
            <el-icon><Shop /></el-icon>
            <span>药店管理</span>
          </el-menu-item>
          
          <el-menu-item v-if="isAdmin" index="/admin">
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <div class="welcome-section">
            <h2>欢迎回来，{{ userStore.user?.username }}！</h2>
            <p v-if="pharmacyStore.currentPharmacy">
              当前药店：{{ pharmacyStore.currentPharmacy.name }}
            </p>
          </div>
          
          <!-- 快捷操作卡片 -->
          <div class="quick-actions">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="action-card" @click="$router.push('/medical-records/new')">
                  <div class="action-content">
                    <el-icon class="action-icon"><Plus /></el-icon>
                    <span>新增就诊记录</span>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="action-card" @click="$router.push('/patients')">
                  <div class="action-content">
                    <el-icon class="action-icon"><User /></el-icon>
                    <span>患者管理</span>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="action-card" @click="$router.push('/quick-medicines')">
                  <div class="action-content">
                    <el-icon class="action-icon"><Medicine /></el-icon>
                    <span>快捷药品</span>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="action-card" @click="$router.push('/statistics')">
                  <div class="action-content">
                    <el-icon class="action-icon"><DataAnalysis /></el-icon>
                    <span>统计报表</span>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House, User, Document, Medicine, DataAnalysis, Shop, Setting,
  ArrowDown, Plus
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { usePharmacyStore } from '@/stores/pharmacy'

const router = useRouter()
const userStore = useUserStore()
const pharmacyStore = usePharmacyStore()

// 当前选中的药店ID
const currentPharmacyId = ref(pharmacyStore.currentPharmacy?.id)

// 计算属性
const isAdmin = computed(() => userStore.user?.role === 'ADMIN')
const userAvatar = computed(() => '')

// 处理药店切换
const handlePharmacyChange = async (pharmacyId: number) => {
  try {
    await pharmacyStore.switchPharmacy(pharmacyId)
    ElMessage.success('药店切换成功')
  } catch (error) {
    console.error('Failed to switch pharmacy:', error)
    ElMessage.error('药店切换失败')
    // 恢复原来的选择
    currentPharmacyId.value = pharmacyStore.currentPharmacy?.id
  }
}

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中')
      break
    case 'changePassword':
      ElMessage.info('修改密码功能开发中')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await userStore.logout()
        pharmacyStore.clearPharmacyState()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-left h1 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.pharmacy-selector {
  width: 200px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
}

.sidebar {
  background: white;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border-right: none;
}

.main-content {
  background: #f5f5f5;
  padding: 0;
}

.welcome-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
  color: #333;
  margin-bottom: 8px;
}

.welcome-section p {
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 10px;
}

.action-icon {
  font-size: 32px;
  color: #409eff;
}

.action-content span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
</style>
