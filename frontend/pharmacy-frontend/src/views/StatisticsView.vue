<template>
  <div class="statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>统计报表</h2>
      <el-button type="primary" @click="exportData" :loading="exporting">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <label>时间范围：</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onDateRangeChange"
          />
        </div>

        <div class="filter-item">
          <label>药店：</label>
          <el-select v-model="selectedPharmacyId" placeholder="选择药店" @change="loadStatistics">
            <el-option label="全部药店" :value="null" />
            <el-option
              v-for="pharmacy in pharmacies"
              :key="pharmacy.id"
              :label="pharmacy.name"
              :value="pharmacy.id"
            />
          </el-select>
        </div>

        <div class="filter-item">
          <el-button-group>
            <el-button
              :type="quickRange === 'today' ? 'primary' : 'default'"
              @click="setQuickRange('today')"
            >
              今天
            </el-button>
            <el-button
              :type="quickRange === 'yesterday' ? 'primary' : 'default'"
              @click="setQuickRange('yesterday')"
            >
              昨天
            </el-button>
            <el-button
              :type="quickRange === 'thisWeek' ? 'primary' : 'default'"
              @click="setQuickRange('thisWeek')"
            >
              本周
            </el-button>
            <el-button
              :type="quickRange === 'thisMonth' ? 'primary' : 'default'"
              @click="setQuickRange('thisMonth')"
            >
              本月
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 基本统计卡片 -->
    <div class="stats-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon new-patients">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ basicStats.newPatients || 0 }}</div>
                <div class="stat-label">新增患者</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon visited-patients">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ basicStats.visitedPatients || 0 }}</div>
                <div class="stat-label">就诊患者</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon infusion-count">
                <el-icon><Medicine /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ basicStats.infusionCount || 0 }}</div>
                <div class="stat-label">输液人数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total-amount">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">¥{{ formatAmount(basicStats.totalAmount) }}</div>
                <div class="stat-label">总金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container" v-loading="loading">
      <el-row :gutter="20">
        <!-- 趋势图 -->
        <el-col :span="24">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>就诊趋势</span>
                <el-radio-group v-model="trendGroupBy" @change="loadTrendData">
                  <el-radio-button label="day">按天</el-radio-button>
                  <el-radio-button label="week">按周</el-radio-button>
                  <el-radio-button label="month">按月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                ref="trendChart"
                :option="trendChartOption"
                :style="{ height: '300px' }"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 性别分布 -->
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>性别分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="genderChartOption"
                :style="{ height: '250px' }"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 年龄分布 -->
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>年龄分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="ageChartOption"
                :style="{ height: '250px' }"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 治疗方式分布 -->
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>治疗方式分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="treatmentChartOption"
                :style="{ height: '250px' }"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  User,
  UserFilled,
  Medicine,
  Money
} from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import {
  CanvasRenderer
} from 'echarts/renderers'
import {
  LineChart,
  PieChart,
  BarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { statisticsApi, type StatisticsParams, type BasicStatistics } from '@/api/statistics'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const dateRange = ref<[string, string]>([])
const selectedPharmacyId = ref<number | null>(null)
const quickRange = ref('today')
const trendGroupBy = ref('day')

// 药店列表
const pharmacies = ref<any[]>([])

// 统计数据
const basicStats = reactive<BasicStatistics>({
  newPatients: 0,
  visitedPatients: 0,
  infusionCount: 0,
  oralCount: 0,
  infusionAmount: 0,
  oralAmount: 0,
  totalAmount: 0
})

const genderData = ref<any>({})
const ageData = ref<any>({})
const treatmentData = ref<any>({})
const trendData = ref<any[]>([])

// 初始化日期范围为今天
const initDateRange = () => {
  const today = new Date()
  const todayStr = today.toISOString().split('T')[0]
  dateRange.value = [todayStr, todayStr]
}

// 设置快捷时间范围
const setQuickRange = (range: string) => {
  quickRange.value = range
  const today = new Date()

  switch (range) {
    case 'today':
      const todayStr = today.toISOString().split('T')[0]
      dateRange.value = [todayStr, todayStr]
      break
    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().split('T')[0]
      dateRange.value = [yesterdayStr, yesterdayStr]
      break
    case 'thisWeek':
      const weekStart = new Date(today)
      weekStart.setDate(today.getDate() - today.getDay())
      const weekStartStr = weekStart.toISOString().split('T')[0]
      const todayStr2 = today.toISOString().split('T')[0]
      dateRange.value = [weekStartStr, todayStr2]
      break
    case 'thisMonth':
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const monthStartStr = monthStart.toISOString().split('T')[0]
      const todayStr3 = today.toISOString().split('T')[0]
      dateRange.value = [monthStartStr, todayStr3]
      break
  }

  loadStatistics()
}

// 日期范围变化
const onDateRangeChange = () => {
  quickRange.value = ''
  loadStatistics()
}

// 格式化金额
const formatAmount = (amount: number | undefined) => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

// 获取统计参数
const getStatisticsParams = (): StatisticsParams => {
  return {
    startDate: dateRange.value[0],
    endDate: dateRange.value[1],
    pharmacyId: selectedPharmacyId.value || undefined
  }
}

// 加载药店列表
const loadPharmacies = async () => {
  try {
    const data = await statisticsApi.getUserPharmacies()
    pharmacies.value = data
  } catch (error) {
    console.error('加载药店列表失败:', error)
  }
}

// 加载统计数据
const loadStatistics = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) return

  loading.value = true
  try {
    const params = getStatisticsParams()

    // 并行加载所有数据
    const [basic, gender, age, treatment] = await Promise.all([
      statisticsApi.getBasicStatistics(params),
      statisticsApi.getGenderDistribution(params),
      statisticsApi.getAgeDistribution(params),
      statisticsApi.getTreatmentDistribution(params)
    ])

    // 更新数据
    Object.assign(basicStats, basic)
    genderData.value = gender
    ageData.value = age
    treatmentData.value = treatment

    // 加载趋势数据
    await loadTrendData()

  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

// 加载趋势数据
const loadTrendData = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) return

  try {
    const params = {
      ...getStatisticsParams(),
      groupBy: trendGroupBy.value as 'day' | 'week' | 'month'
    }

    const data = await statisticsApi.getTrendData(params)
    trendData.value = data
  } catch (error) {
    console.error('加载趋势数据失败:', error)
  }
}

// 导出数据
const exportData = async () => {
  if (!dateRange.value[0] || !dateRange.value[1]) {
    ElMessage.warning('请先选择时间范围')
    return
  }

  exporting.value = true
  try {
    const params = getStatisticsParams()
    const response = await statisticsApi.exportStatistics(params)

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `统计数据_${dateRange.value[0]}至${dateRange.value[1]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  } finally {
    exporting.value = false
  }
}

// 图表配置
const trendChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['新增患者', '就诊患者', '输液人数']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: trendData.value.map(item => item.date)
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '新增患者',
      type: 'line',
      data: trendData.value.map(item => item.newPatients),
      smooth: true,
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '就诊患者',
      type: 'line',
      data: trendData.value.map(item => item.visitedPatients),
      smooth: true,
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '输液人数',
      type: 'line',
      data: trendData.value.map(item => item.infusionCount),
      smooth: true,
      itemStyle: { color: '#E6A23C' }
    }
  ]
}))

const genderChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '性别分布',
      type: 'pie',
      radius: '50%',
      data: [
        { value: genderData.value.male || 0, name: '男性' },
        { value: genderData.value.female || 0, name: '女性' },
        { value: genderData.value.unknown || 0, name: '未知' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

const ageChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: Object.keys(ageData.value)
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '人数',
      type: 'bar',
      data: Object.values(ageData.value),
      itemStyle: { color: '#409EFF' }
    }
  ]
}))

const treatmentChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '治疗方式',
      type: 'pie',
      radius: '50%',
      data: [
        { value: treatmentData.value.infusion || 0, name: '仅输液' },
        { value: treatmentData.value.oral || 0, name: '仅口服' },
        { value: treatmentData.value.both || 0, name: '输液+口服' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 生命周期
onMounted(async () => {
  initDateRange()
  await loadPharmacies()
  await loadStatistics()
})
</script>

<style scoped>
.statistics-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 10px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.new-patients {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.visited-patients {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.infusion-count {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.total-amount {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-container {
  margin-top: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-item {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 4px;
}

/* 图表容器样式 */
.chart-container .echarts {
  width: 100% !important;
}
</style>
