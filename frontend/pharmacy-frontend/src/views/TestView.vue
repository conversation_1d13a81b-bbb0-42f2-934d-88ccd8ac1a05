<template>
  <div class="test-container">
    <h1>测试页面</h1>
    <p>如果您看到这个页面，说明Vue应用正在正常工作。</p>
    <el-button type="primary" @click="goToLogin">去登录页面</el-button>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.test-container {
  padding: 50px;
  text-align: center;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

p {
  color: #666;
  margin-bottom: 30px;
}
</style>
