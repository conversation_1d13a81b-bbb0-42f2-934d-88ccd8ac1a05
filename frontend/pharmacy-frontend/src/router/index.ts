import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/pharmacy/select',
    name: 'PharmacySelect',
    component: () => import('@/views/pharmacy/PharmacySelectView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/pharmacies',
    name: 'Pharmacies',
    component: () => import('@/views/pharmacy/PharmacyListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/patients',
    name: 'Patients',
    component: () => import('@/views/patient/PatientListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/patients/:id',
    name: 'PatientDetail',
    component: () => import('@/views/patient/PatientDetailView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records',
    name: 'MedicalRecords',
    component: () => import('@/views/medical/MedicalRecordListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records/new',
    name: 'NewMedicalRecord',
    component: () => import('@/views/medical/MedicalRecordFormView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records/:id',
    name: 'MedicalRecordDetail',
    component: () => import('@/views/medical/MedicalRecordDetailView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/quick-medicines',
    name: 'QuickMedicines',
    component: () => import('@/views/medicine/QuickMedicineListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/StatisticsView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/admin/AdminView.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  console.log('路由守卫:', to.path, to.name)

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('access_token')
    if (!token) {
      console.log('需要认证但没有token，跳转到登录页')
      next('/login')
      return
    }
  }

  console.log('路由守卫通过，继续导航')
  next()
})

export default router
