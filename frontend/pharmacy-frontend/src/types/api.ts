// API响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页响应类型
export interface PageResponse<T = any> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

// 用户相关类型
export interface User {
  id: number
  username: string
  realName?: string
  role: string
  pharmacies?: Pharmacy[]
  currentPharmacyId?: number
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  userInfo: User
}

// 药店相关类型
export interface Pharmacy {
  id: number
  name: string
  address: string
  phone: string
  description?: string
  businessHours?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface PharmacyRequest {
  name: string
  address: string
  phone: string
  description?: string
}

// 患者相关类型
export interface Patient {
  id: number
  name: string
  gender: string
  age: number
  phone: string
  address: string
  avatar?: string
  pharmacyId: number
  createdAt: string
  updatedAt: string
}

export interface PatientRequest {
  name: string
  gender: string
  age: number
  phone: string
  address: string
  pharmacyId: number
}

// 就诊记录相关类型
export interface MedicalRecord {
  id: number
  patientId: number
  patientName: string
  symptoms: string
  diagnosis: string
  prescription: string
  notes: string
  visitDate: string
  pharmacyId: number
  images: MedicalRecordImage[]
  createdAt: string
  updatedAt: string
}

export interface MedicalRecordImage {
  id: number
  filename: string
  originalName: string
  url: string
  medicalRecordId: number
}

export interface MedicalRecordRequest {
  patientId: number
  symptoms: string
  diagnosis: string
  prescription: string
  notes?: string
  visitDate: string
  pharmacyId: number
}

// 快捷药品相关类型
export interface QuickMedicine {
  id: number
  name: string
  type: string
  specification: string
  usage: string
  dosage: string
  notes: string
  createdAt: string
  updatedAt: string
}

export interface QuickMedicineRequest {
  name: string
  type: string
  specification: string
  usage: string
  dosage: string
  notes?: string
}

// 分页查询参数
export interface PageRequest {
  page?: number
  size?: number
  sort?: string
  direction?: 'ASC' | 'DESC'
}

// 搜索参数
export interface SearchRequest extends PageRequest {
  keyword?: string
}
