<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { usePharmacyStore } from '@/stores/pharmacy'

console.log('App.vue 加载成功')

const userStore = useUserStore()
const pharmacyStore = usePharmacyStore()

// 应用初始化
onMounted(() => {
  console.log('App.vue onMounted')
  try {
    // 初始化认证状态
    userStore.initializeAuth()
    console.log('用户认证状态初始化完成')

    // 初始化药店状态
    pharmacyStore.initializePharmacy()
    console.log('药店状态初始化完成')

  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  height: 100%;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-wrapper {
  padding: 20px;
}

/* Element Plus 样式覆盖 */
.el-menu--horizontal .el-menu-item {
  border-bottom: none !important;
}

.el-table .cell {
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 10px;
  }
}
</style>
