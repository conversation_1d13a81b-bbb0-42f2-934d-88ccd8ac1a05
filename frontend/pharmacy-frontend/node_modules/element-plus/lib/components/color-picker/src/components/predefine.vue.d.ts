import Color from '../utils/color';
import type { PropType, Ref } from 'vue';
declare const _default: import("vue").DefineComponent<{
    colors: {
        type: PropType<string[]>;
        required: true;
    };
    color: {
        type: PropType<Color>;
        required: true;
    };
    enableAlpha: {
        type: BooleanConstructor;
        required: true;
    };
}, {
    rgbaColors: Ref<Color[]>;
    handleSelect: (index: number) => void;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    colors: {
        type: PropType<string[]>;
        required: true;
    };
    color: {
        type: PropType<Color>;
        required: true;
    };
    enableAlpha: {
        type: BooleanConstructor;
        required: true;
    };
}>>, {}>;
export default _default;
