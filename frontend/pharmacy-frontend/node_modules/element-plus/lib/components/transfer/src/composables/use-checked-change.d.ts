import type { SetupContext } from 'vue';
import type { TransferCheckedState, TransferEmits, TransferKey } from '../transfer';
export declare const useCheckedChange: (checkedState: TransferCheckedState, emit: SetupContext<TransferEmits>["emit"]) => {
    onSourceCheckedChange: (val: TransferKey[], movedKeys?: TransferKey[]) => void;
    onTargetCheckedChange: (val: TransferKey[], movedKeys?: TransferKey[]) => void;
};
