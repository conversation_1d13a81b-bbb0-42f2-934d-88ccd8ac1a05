version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: pharmacy-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: pharmacy_db
      MYSQL_USER: pharmacy_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-pharmacy123456}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./database/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - pharmacy-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端应用
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: pharmacy-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: pharmacy_db
      DB_USERNAME: pharmacy_user
      DB_PASSWORD: ${MYSQL_PASSWORD:-pharmacy123456}
      JWT_SECRET: ${JWT_SECRET:-your_production_secret_key_here_must_be_at_least_256_bits}
    ports:
      - "8080:8080"
    volumes:
      - backend_logs:/var/log/pharmacy
      - backend_uploads:/var/pharmacy/uploads
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - pharmacy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端应用
  frontend:
    build:
      context: ./frontend/pharmacy-frontend
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: ${FRONTEND_API_URL:-http://localhost:8080/api/v1}
    container_name: pharmacy-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - backend_uploads:/var/pharmacy/uploads:ro
    depends_on:
      - backend
    networks:
      - pharmacy-network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: pharmacy-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123456}
    networks:
      - pharmacy-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local

networks:
  pharmacy-network:
    driver: bridge
