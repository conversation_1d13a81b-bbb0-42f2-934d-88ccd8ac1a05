package com.pharmacy.management.service;

import com.pharmacy.management.entity.Patient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface PatientService {
    List<Patient> findAll();
    Patient findById(Long id);
    Optional<Patient> findByName(String name);
    Optional<Patient> findByNameAndPharmacyId(String name, Long pharmacyId);
    boolean existsByName(String name);
    boolean existsByNameAndPharmacyId(String name, Long pharmacyId);
    Patient save(Patient patient);
    void deleteById(Long id);
    Page<Patient> findAllByOrderByCreatedAtDesc(Pageable pageable);
    List<Patient> searchPatients(String term);
    
    // 根据创建时间范围查询患者
    List<Patient> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    // 统计指定时间范围内创建的患者数量
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    List<Patient> findByPharmacyIdAndCreatedAtBetween(Long pharmacyId, LocalDateTime start, LocalDateTime end);
    Page<Patient> findByPharmacyId(Long pharmacyId, Pageable pageable);
    
    // 统计指定药店的患者数量
    long countByPharmacyId(Long pharmacyId);

    // API专用方法
    Patient getPatientById(Long id);
    Patient savePatient(Patient patient);
    void deletePatient(Long id);
    boolean canDeletePatient(Long id);
    Page<Patient> getPatientsByPharmacy(Long pharmacyId, Pageable pageable);
    Page<Patient> searchPatients(String keyword, Long pharmacyId, Pageable pageable);
    String uploadAvatar(Long patientId, MultipartFile file) throws Exception;
    Map<String, Object> getPatientStatistics(Long pharmacyId);
}