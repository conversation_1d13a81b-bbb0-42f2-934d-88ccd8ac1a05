package com.pharmacy.management.service.impl;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.entity.Patient;
import com.pharmacy.management.repository.PatientRepository;
import com.pharmacy.management.service.PatientService;
import com.pharmacy.management.service.MedicalRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class PatientServiceImpl implements PatientService {

    private final PatientRepository patientRepository;
    private final PharmacyContext pharmacyContext;
    private final MedicalRecordService medicalRecordService;

    @Autowired
    public PatientServiceImpl(PatientRepository patientRepository, PharmacyContext pharmacyContext, MedicalRecordService medicalRecordService) {
        this.patientRepository = patientRepository;
        this.pharmacyContext = pharmacyContext;
        this.medicalRecordService = medicalRecordService;
    }

    @Override
    public List<Patient> findAll() {
        if (pharmacyContext.getCurrentPharmacy() != null) {
            return patientRepository.findByPharmacyId(pharmacyContext.getCurrentPharmacyId());
        }
        return patientRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Patient findById(Long id) {
        Patient patient = patientRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("患者不存在: " + id));
        
        // 获取当前用户的角色
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        
        // 验证患者是否属于当前药店（管理员可以查看所有患者）
        if (!isAdmin && pharmacyContext.getCurrentPharmacy() != null && 
            !patient.getPharmacy().getId().equals(pharmacyContext.getCurrentPharmacyId())) {
            throw new SecurityException("无权访问其他药店的患者信息");
        }
        
        return patient;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Patient> findByName(String name) {
        return patientRepository.findByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Patient> findByNameAndPharmacyId(String name, Long pharmacyId) {
        return patientRepository.findByNameAndPharmacyId(name, pharmacyId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return patientRepository.existsByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByNameAndPharmacyId(String name, Long pharmacyId) {
        return patientRepository.existsByNameAndPharmacyId(name, pharmacyId);
    }

    @Override
    public Patient save(Patient patient) {
        // 确保设置当前药店
        if (patient.getPharmacy() == null && pharmacyContext.getCurrentPharmacy() != null) {
            patient.setPharmacy(pharmacyContext.getCurrentPharmacy());
        }
        
        // 检查同一药店内是否已存在同名患者
        if (patient.getId() == null) { // 新增患者时检查
            if (patientRepository.existsByNameAndPharmacyId(patient.getName(), patient.getPharmacy().getId())) {
                throw new IllegalArgumentException("该药店内已存在同名患者: " + patient.getName());
            }
        } else { // 更新患者时检查
            Optional<Patient> existingPatient = patientRepository.findByNameAndPharmacyId(
                patient.getName(), patient.getPharmacy().getId());
            if (existingPatient.isPresent() && !existingPatient.get().getId().equals(patient.getId())) {
                throw new IllegalArgumentException("该药店内已存在同名患者: " + patient.getName());
            }
        }
        
        return patientRepository.save(patient);
    }

    @Override
    public void deleteById(Long id) {
        // 先检查患者是否属于当前药店
        Patient patient = findById(id);
        
        // 检查患者是否有关联的医疗记录
        long recordCount = medicalRecordService.countByPatientId(id);
        if (recordCount > 0) {
            throw new IllegalStateException("无法删除患者，该患者有 " + recordCount + " 条就诊记录关联。请先删除相关就诊记录。");
        }
        
        patientRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Patient> findAllByOrderByCreatedAtDesc(Pageable pageable) {
        if (pharmacyContext.getCurrentPharmacy() != null) {
            return patientRepository.findByPharmacyIdOrderByCreatedAtDesc(
                    pharmacyContext.getCurrentPharmacyId(), pageable);
        }
        return patientRepository.findAllByOrderByCreatedAtDesc(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Patient> searchPatients(String term) {
        if (pharmacyContext.getCurrentPharmacy() != null) {
            return patientRepository.searchPatientsByPharmacy(
                    pharmacyContext.getCurrentPharmacyId(), term);
        }
        return patientRepository.searchByTerm(term);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Patient> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime) {
        if (pharmacyContext.getCurrentPharmacy() != null) {
            return patientRepository.findByPharmacyIdAndCreatedAtBetween(
                    pharmacyContext.getCurrentPharmacyId(), startTime, endTime);
        }
        return patientRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime) {
        if (pharmacyContext.getCurrentPharmacy() != null) {
            return patientRepository.countByCreatedAtBetweenAndPharmacyId(
                    startTime, endTime, pharmacyContext.getCurrentPharmacyId());
        }
        return patientRepository.countByCreatedAtBetween(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Patient> findByPharmacyIdAndCreatedAtBetween(Long pharmacyId, LocalDateTime start, LocalDateTime end) {
        // 验证是否有权限访问指定药店的数据
        if (pharmacyContext.getCurrentPharmacy() != null && 
            !pharmacyContext.getCurrentPharmacyId().equals(pharmacyId)) {
            throw new SecurityException("无权访问其他药店的患者信息");
        }
        
        return patientRepository.findByPharmacyIdAndCreatedAtBetween(pharmacyId, start, end);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Patient> findByPharmacyId(Long pharmacyId, Pageable pageable) {
        // 验证是否有权限访问指定药店的数据
        if (pharmacyContext.getCurrentPharmacy() != null && 
            !pharmacyContext.getCurrentPharmacyId().equals(pharmacyId)) {
            throw new SecurityException("无权访问其他药店的患者信息");
        }
        
        return patientRepository.findByPharmacyId(pharmacyId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public long countByPharmacyId(Long pharmacyId) {
        return patientRepository.countByPharmacyId(pharmacyId);
    }

    // API专用方法实现
    @Override
    @Transactional(readOnly = true)
    public Patient getPatientById(Long id) {
        return patientRepository.findById(id).orElse(null);
    }

    @Override
    @Transactional
    public Patient savePatient(Patient patient) {
        return save(patient);
    }

    @Override
    @Transactional
    public void deletePatient(Long id) {
        deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDeletePatient(Long id) {
        try {
            long recordCount = medicalRecordService.countByPatientId(id);
            return recordCount == 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Patient> getPatientsByPharmacy(Long pharmacyId, Pageable pageable) {
        return findByPharmacyId(pharmacyId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Patient> searchPatients(String keyword, Long pharmacyId, Pageable pageable) {
        return patientRepository.searchPatientsByPharmacyWithPaging(pharmacyId, keyword, pageable);
    }

    @Override
    @Transactional
    public String uploadAvatar(Long patientId, MultipartFile file) throws Exception {
        Patient patient = getPatientById(patientId);
        if (patient == null) {
            throw new EntityNotFoundException("患者不存在");
        }

        // 检查文件大小
        long maxFileSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("文件大小超过限制，最大允许 10MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.startsWith("image/jpeg") && !contentType.startsWith("image/png"))) {
            throw new IllegalArgumentException("只支持JPG和PNG格式的图片");
        }

        // 删除旧头像
        if (patient.getAvatarUrl() != null) {
            // 这里需要实现文件删除逻辑
        }

        // 保存新头像
        String avatarUrl = saveAndCompressAvatar(file);
        patient.setAvatarUrl(avatarUrl);
        savePatient(patient);

        return avatarUrl;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getPatientStatistics(Long pharmacyId) {
        long totalPatients = countByPharmacyId(pharmacyId);

        // 获取本月新增患者数量
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime monthEnd = LocalDateTime.now();
        long monthlyNewPatients = patientRepository.countByPharmacyIdAndCreatedAtBetween(pharmacyId, monthStart, monthEnd);

        // 获取今日新增患者数量
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now();
        long dailyNewPatients = patientRepository.countByPharmacyIdAndCreatedAtBetween(pharmacyId, todayStart, todayEnd);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPatients", totalPatients);
        statistics.put("monthlyNewPatients", monthlyNewPatients);
        statistics.put("dailyNewPatients", dailyNewPatients);

        return statistics;
    }

    // 私有辅助方法
    private String saveAndCompressAvatar(MultipartFile file) throws Exception {
        // 这里需要实现头像保存和压缩逻辑
        // 暂时返回一个占位符
        return "/uploads/avatars/placeholder.jpg";
    }
}