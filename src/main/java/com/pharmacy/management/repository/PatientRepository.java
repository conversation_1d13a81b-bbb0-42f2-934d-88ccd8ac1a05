package com.pharmacy.management.repository;

import com.pharmacy.management.entity.Patient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PatientRepository extends JpaRepository<Patient, Long> {
    Optional<Patient> findByName(String name);
    Optional<Patient> findByNameAndPharmacyId(String name, Long pharmacyId);
    boolean existsByName(String name);
    boolean existsByNameAndPharmacyId(String name, Long pharmacyId);
    Page<Patient> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    @Query("SELECT p FROM Patient p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.phoneNumber) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.idCard) LIKE LOWER(CONCAT('%', :term, '%'))")
    List<Patient> searchByTerm(@Param("term") String term);
    
    // 根据创建时间范围查询患者
    List<Patient> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    // 统计指定时间范围内创建的患者数量
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    // 统计指定时间范围和药店内创建的患者数量
    long countByCreatedAtBetweenAndPharmacyId(LocalDateTime startTime, LocalDateTime endTime, Long pharmacyId);

    Page<Patient> findByPharmacyIdOrderByCreatedAtDesc(Long pharmacyId, Pageable pageable);
    
    @Query("SELECT p FROM Patient p WHERE p.pharmacy.id = :pharmacyId AND " +
           "(LOWER(p.name) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.phoneNumber) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.idCard) LIKE LOWER(CONCAT('%', :term, '%')))")
    List<Patient> searchPatientsByPharmacy(@Param("pharmacyId") Long pharmacyId, @Param("term") String term);
    
    @Query("SELECT p FROM Patient p WHERE p.pharmacy.id = :pharmacyId AND " +
           "(p.name LIKE %:term% OR p.phoneNumber LIKE %:term% OR p.idCard LIKE %:term%)")
    Page<Patient> searchPatientsByPharmacy(@Param("pharmacyId") Long pharmacyId, @Param("term") String term, Pageable pageable);

    List<Patient> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);

    List<Patient> findByPharmacyIdAndCreatedAtBetween(Long pharmacyId, LocalDateTime start, LocalDateTime end);

    Page<Patient> findByPharmacyId(Long pharmacyId, Pageable pageable);
    
    List<Patient> findByPharmacyId(Long pharmacyId);
    
    // 统计指定药店的患者数量
    long countByPharmacyId(Long pharmacyId);

    // 分页搜索指定药店的患者
    @Query("SELECT p FROM Patient p WHERE p.pharmacy.id = :pharmacyId AND " +
           "(LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.phoneNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.idCard) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Patient> searchPatientsByPharmacyWithPaging(@Param("pharmacyId") Long pharmacyId, @Param("keyword") String keyword, Pageable pageable);

    // 统计指定药店和时间范围内的患者数量
    long countByPharmacyIdAndCreatedAtBetween(Long pharmacyId, LocalDateTime startTime, LocalDateTime endTime);
}