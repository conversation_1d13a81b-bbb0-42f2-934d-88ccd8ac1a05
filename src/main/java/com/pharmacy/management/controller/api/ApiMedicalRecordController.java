package com.pharmacy.management.controller.api;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.dto.PageResponse;
import com.pharmacy.management.entity.MedicalRecord;
import com.pharmacy.management.entity.MedicalRecordImage;
import com.pharmacy.management.entity.Patient;
import com.pharmacy.management.service.MedicalRecordService;
import com.pharmacy.management.service.MedicalRecordImageService;
import com.pharmacy.management.service.PatientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 就诊记录管理API控制器
 */
@RestController
@RequestMapping("/api/v1/medical-records")
public class ApiMedicalRecordController {

    private static final Logger log = LoggerFactory.getLogger(ApiMedicalRecordController.class);

    @Autowired
    private MedicalRecordService medicalRecordService;

    @Autowired
    private MedicalRecordImageService medicalRecordImageService;

    @Autowired
    private PatientService patientService;

    @Autowired
    private PharmacyContext pharmacyContext;

    /**
     * 获取就诊记录列表（分页）
     */
    @GetMapping
    public ApiResponse<PageResponse<MedicalRecord>> getMedicalRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createTime") String sort,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) Long patientId) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 创建分页参数
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

            Page<MedicalRecord> records;
            if (patientId != null) {
                // 获取指定患者的就诊记录
                records = medicalRecordService.findByPatientId(patientId, pageable);
                log.info("获取患者就诊记录，患者ID: {}, 结果数量: {}", patientId, records.getTotalElements());
            } else {
                // 获取当前药店的所有就诊记录
                records = medicalRecordService.findAll(pageable);
                log.info("获取就诊记录列表，药店ID: {}, 结果数量: {}", 
                    pharmacyContext.getCurrentPharmacyId(), records.getTotalElements());
            }

            PageResponse<MedicalRecord> response = PageResponse.of(records);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取就诊记录列表失败: {}", e.getMessage());
            return ApiResponse.error("获取就诊记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取就诊记录详情
     */
    @GetMapping("/{id}")
    public ApiResponse<MedicalRecord> getMedicalRecordById(@PathVariable Long id) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            MedicalRecord record = medicalRecordService.findById(id);
            if (record == null) {
                return ApiResponse.notFound("就诊记录不存在");
            }

            return ApiResponse.success(record);
        } catch (SecurityException e) {
            log.warn("无权访问就诊记录: {}", e.getMessage());
            return ApiResponse.forbidden("您没有权限访问该就诊记录");
        } catch (Exception e) {
            log.error("获取就诊记录详情失败: {}", e.getMessage());
            return ApiResponse.error("获取就诊记录详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建就诊记录
     */
    @PostMapping
    public ApiResponse<MedicalRecord> createMedicalRecord(@RequestBody MedicalRecord medicalRecord) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 验证患者ID
            if (medicalRecord.getPatient() == null || medicalRecord.getPatient().getId() == null) {
                return ApiResponse.badRequest("患者ID不能为空");
            }

            // 验证患者是否存在
            Patient patient = patientService.getPatientById(medicalRecord.getPatient().getId());
            if (patient == null) {
                return ApiResponse.badRequest("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!patient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限为该患者创建就诊记录");
            }

            // 设置关联信息
            medicalRecord.setPatient(patient);
            medicalRecord.setPharmacy(pharmacyContext.getCurrentPharmacy());
            medicalRecord.setCreateTime(LocalDateTime.now());

            MedicalRecord savedRecord = medicalRecordService.save(medicalRecord);
            log.info("创建就诊记录成功: 患者ID {}, 记录ID {}", patient.getId(), savedRecord.getId());
            return ApiResponse.success("就诊记录创建成功", savedRecord);
        } catch (Exception e) {
            log.error("创建就诊记录失败: {}", e.getMessage());
            return ApiResponse.error("创建就诊记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新就诊记录
     */
    @PutMapping("/{id}")
    public ApiResponse<MedicalRecord> updateMedicalRecord(@PathVariable Long id, @RequestBody MedicalRecord medicalRecord) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查记录是否存在
            MedicalRecord existingRecord = medicalRecordService.findById(id);
            if (existingRecord == null) {
                return ApiResponse.notFound("就诊记录不存在");
            }

            // 验证记录是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!existingRecord.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限修改该就诊记录");
            }

            // 更新记录信息
            medicalRecord.setId(id);
            medicalRecord.setPatient(existingRecord.getPatient()); // 保持患者不变
            medicalRecord.setPharmacy(existingRecord.getPharmacy()); // 保持药店不变
            medicalRecord.setCreateTime(existingRecord.getCreateTime()); // 保持创建时间不变
            medicalRecord.setUpdateTime(LocalDateTime.now());

            MedicalRecord updatedRecord = medicalRecordService.save(medicalRecord);
            log.info("更新就诊记录成功: 记录ID {}", id);
            return ApiResponse.success("就诊记录更新成功", updatedRecord);
        } catch (SecurityException e) {
            log.warn("无权修改就诊记录: {}", e.getMessage());
            return ApiResponse.forbidden("您没有权限修改该就诊记录");
        } catch (Exception e) {
            log.error("更新就诊记录失败: {}", e.getMessage());
            return ApiResponse.error("更新就诊记录失败: " + e.getMessage());
        }
    }

    /**
     * 删除就诊记录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteMedicalRecord(@PathVariable Long id) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查记录是否存在
            MedicalRecord record = medicalRecordService.findById(id);
            if (record == null) {
                return ApiResponse.notFound("就诊记录不存在");
            }

            // 验证记录是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!record.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限删除该就诊记录");
            }

            medicalRecordService.deleteById(id);
            log.info("删除就诊记录成功: 记录ID {}", id);
            return ApiResponse.success("就诊记录删除成功");
        } catch (SecurityException e) {
            log.warn("无权删除就诊记录: {}", e.getMessage());
            return ApiResponse.forbidden("您没有权限删除该就诊记录");
        } catch (Exception e) {
            log.error("删除就诊记录失败: {}", e.getMessage());
            return ApiResponse.error("删除就诊记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者历史就诊记录
     */
    @GetMapping("/patient/{patientId}/history")
    public ApiResponse<List<MedicalRecord>> getPatientHistory(@PathVariable Long patientId) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 验证患者是否存在
            Patient patient = patientService.getPatientById(patientId);
            if (patient == null) {
                return ApiResponse.notFound("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!patient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该患者的就诊记录");
            }

            List<MedicalRecord> records = medicalRecordService.findByPatientId(patientId);
            log.info("获取患者历史记录成功: 患者ID {}, 记录数量 {}", patientId, records.size());
            return ApiResponse.success(records);
        } catch (Exception e) {
            log.error("获取患者历史记录失败: {}", e.getMessage());
            return ApiResponse.error("获取患者历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 上传就诊记录图片
     */
    @PostMapping("/{id}/images")
    public ApiResponse<Map<String, Object>> uploadImages(
            @PathVariable Long id, 
            @RequestParam("files") MultipartFile[] files) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查记录是否存在
            MedicalRecord record = medicalRecordService.findById(id);
            if (record == null) {
                return ApiResponse.notFound("就诊记录不存在");
            }

            // 验证记录是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!record.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限为该就诊记录上传图片");
            }

            // TODO: 实现图片上传逻辑
            // 这里需要实现具体的文件上传和保存逻辑
            
            Map<String, Object> result = new HashMap<>();
            result.put("recordId", id);
            result.put("uploadedCount", files.length);
            
            log.info("上传就诊记录图片成功: 记录ID {}, 图片数量 {}", id, files.length);
            return ApiResponse.success("图片上传成功", result);
        } catch (SecurityException e) {
            log.warn("无权上传就诊记录图片: {}", e.getMessage());
            return ApiResponse.forbidden("您没有权限为该就诊记录上传图片");
        } catch (Exception e) {
            log.error("上传就诊记录图片失败: {}", e.getMessage());
            return ApiResponse.error("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取就诊记录统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getMedicalRecordStatistics() {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            Long pharmacyId = pharmacyContext.getCurrentPharmacyId();
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalRecords", medicalRecordService.countByPharmacyId(pharmacyId));
            
            // TODO: 添加更多统计信息，如今日就诊数量、本月就诊数量等
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取就诊记录统计信息失败: {}", e.getMessage());
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
