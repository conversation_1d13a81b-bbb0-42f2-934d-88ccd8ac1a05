package com.pharmacy.management.controller.api;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.entity.Patient;
import com.pharmacy.management.service.PatientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 患者管理API控制器
 */
@RestController
@RequestMapping("/api/v1/patients")
public class ApiPatientController {

    private static final Logger log = LoggerFactory.getLogger(ApiPatientController.class);

    @Autowired
    private PatientService patientService;

    @Autowired
    private PharmacyContext pharmacyContext;

    /**
     * 获取患者列表（分页）
     */
    @GetMapping
    public ApiResponse<Page<Patient>> getPatients(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String keyword) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            Long pharmacyId = pharmacyContext.getCurrentPharmacyId();
            
            // 创建分页参数
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

            Page<Patient> patients;
            if (keyword != null && !keyword.trim().isEmpty()) {
                // 搜索患者
                patients = patientService.searchPatients(keyword.trim(), pharmacyId, pageable);
                log.info("搜索患者，关键词: {}, 药店ID: {}, 结果数量: {}", keyword, pharmacyId, patients.getTotalElements());
            } else {
                // 获取所有患者
                patients = patientService.getPatientsByPharmacy(pharmacyId, pageable);
                log.info("获取患者列表，药店ID: {}, 结果数量: {}", pharmacyId, patients.getTotalElements());
            }

            return ApiResponse.success(patients);
        } catch (Exception e) {
            log.error("获取患者列表失败: {}", e.getMessage());
            return ApiResponse.error("获取患者列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Patient> getPatientById(@PathVariable Long id) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            Patient patient = patientService.getPatientById(id);
            if (patient == null) {
                return ApiResponse.notFound("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!patient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该患者信息");
            }

            return ApiResponse.success(patient);
        } catch (Exception e) {
            log.error("获取患者详情失败: {}", e.getMessage());
            return ApiResponse.error("获取患者详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建患者
     */
    @PostMapping
    public ApiResponse<Patient> createPatient(@RequestBody Patient patient) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 设置药店
            patient.setPharmacy(pharmacyContext.getCurrentPharmacy());

            // 验证必填字段
            if (patient.getName() == null || patient.getName().trim().isEmpty()) {
                return ApiResponse.badRequest("患者姓名不能为空");
            }
            if (patient.getPhoneNumber() == null || patient.getPhoneNumber().trim().isEmpty()) {
                return ApiResponse.badRequest("联系电话不能为空");
            }

            Patient savedPatient = patientService.savePatient(patient);
            log.info("创建患者成功: {} (ID: {})", savedPatient.getName(), savedPatient.getId());
            return ApiResponse.success("患者创建成功", savedPatient);
        } catch (Exception e) {
            log.error("创建患者失败: {}", e.getMessage());
            return ApiResponse.error("创建患者失败: " + e.getMessage());
        }
    }

    /**
     * 更新患者
     */
    @PutMapping("/{id}")
    public ApiResponse<Patient> updatePatient(@PathVariable Long id, @RequestBody Patient patient) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查患者是否存在
            Patient existingPatient = patientService.getPatientById(id);
            if (existingPatient == null) {
                return ApiResponse.notFound("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!existingPatient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限修改该患者信息");
            }

            // 验证必填字段
            if (patient.getName() == null || patient.getName().trim().isEmpty()) {
                return ApiResponse.badRequest("患者姓名不能为空");
            }
            if (patient.getPhoneNumber() == null || patient.getPhoneNumber().trim().isEmpty()) {
                return ApiResponse.badRequest("联系电话不能为空");
            }

            // 更新患者信息
            patient.setId(id);
            patient.setPharmacy(pharmacyContext.getCurrentPharmacy()); // 确保药店不被修改
            Patient updatedPatient = patientService.savePatient(patient);
            
            log.info("更新患者成功: {} (ID: {})", updatedPatient.getName(), id);
            return ApiResponse.success("患者信息更新成功", updatedPatient);
        } catch (Exception e) {
            log.error("更新患者失败: {}", e.getMessage());
            return ApiResponse.error("更新患者失败: " + e.getMessage());
        }
    }

    /**
     * 删除患者
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deletePatient(@PathVariable Long id) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查患者是否存在
            Patient patient = patientService.getPatientById(id);
            if (patient == null) {
                return ApiResponse.notFound("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!patient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限删除该患者信息");
            }

            // 检查是否可以删除（是否有关联的就诊记录）
            if (!patientService.canDeletePatient(id)) {
                return ApiResponse.badRequest("该患者有关联的就诊记录，无法删除");
            }

            patientService.deletePatient(id);
            log.info("删除患者成功: ID {}", id);
            return ApiResponse.success("患者删除成功");
        } catch (Exception e) {
            log.error("删除患者失败: {}", e.getMessage());
            return ApiResponse.error("删除患者失败: " + e.getMessage());
        }
    }

    /**
     * 上传患者头像
     */
    @PostMapping("/{id}/avatar")
    public ApiResponse<Map<String, String>> uploadAvatar(
            @PathVariable Long id, 
            @RequestParam("file") MultipartFile file) {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            // 检查患者是否存在
            Patient patient = patientService.getPatientById(id);
            if (patient == null) {
                return ApiResponse.notFound("患者不存在");
            }

            // 验证患者是否属于当前药店
            Long currentPharmacyId = pharmacyContext.getCurrentPharmacyId();
            if (!patient.getPharmacy().getId().equals(currentPharmacyId)) {
                return ApiResponse.forbidden("您没有权限修改该患者信息");
            }

            // 上传头像
            String avatarUrl = patientService.uploadAvatar(id, file);
            
            Map<String, String> result = new HashMap<>();
            result.put("avatarUrl", avatarUrl);
            log.info("上传患者头像成功: 患者ID {}, 头像URL: {}", id, avatarUrl);
            return ApiResponse.success("头像上传成功", result);
        } catch (Exception e) {
            log.error("上传患者头像失败: {}", e.getMessage());
            return ApiResponse.error("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getPatientStatistics() {
        try {
            // 检查当前药店
            if (pharmacyContext.getCurrentPharmacy() == null) {
                return ApiResponse.badRequest("请先选择药店");
            }

            Long pharmacyId = pharmacyContext.getCurrentPharmacyId();
            Map<String, Object> statistics = patientService.getPatientStatistics(pharmacyId);
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取患者统计信息失败: {}", e.getMessage());
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
