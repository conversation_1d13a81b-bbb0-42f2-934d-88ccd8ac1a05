package com.pharmacy.management.controller.api;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.entity.Pharmacy;
import com.pharmacy.management.service.PharmacyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 药店管理API控制器
 */
@RestController
@RequestMapping("/api/v1/pharmacies")
public class ApiPharmacyController {

    private static final Logger log = LoggerFactory.getLogger(ApiPharmacyController.class);

    @Autowired
    private PharmacyService pharmacyService;

    @Autowired
    private PharmacyContext pharmacyContext;

    /**
     * 获取当前用户可访问的药店列表
     */
    @GetMapping("/user")
    public ApiResponse<List<Pharmacy>> getUserPharmacies() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            
            List<Pharmacy> pharmacies = pharmacyService.getPharmaciesForUser(username);
            log.info("用户 {} 获取药店列表，数量: {}", username, pharmacies.size());
            
            return ApiResponse.success(pharmacies);
        } catch (Exception e) {
            log.error("获取用户药店列表失败: {}", e.getMessage());
            return ApiResponse.error("获取药店列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有药店列表（管理员权限）
     */
    @GetMapping
    public ApiResponse<List<Pharmacy>> getAllPharmacies() {
        try {
            List<Pharmacy> pharmacies = pharmacyService.getAllPharmacies();
            return ApiResponse.success(pharmacies);
        } catch (Exception e) {
            log.error("获取所有药店列表失败: {}", e.getMessage());
            return ApiResponse.error("获取药店列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前选中的药店
     */
    @GetMapping("/current")
    public ApiResponse<Pharmacy> getCurrentPharmacy() {
        try {
            Pharmacy currentPharmacy = pharmacyContext.getCurrentPharmacy();
            if (currentPharmacy == null) {
                return ApiResponse.success("当前未选择药店", null);
            }
            return ApiResponse.success(currentPharmacy);
        } catch (Exception e) {
            log.error("获取当前药店失败: {}", e.getMessage());
            return ApiResponse.error("获取当前药店失败: " + e.getMessage());
        }
    }

    /**
     * 切换药店
     */
    @PostMapping("/switch")
    public ApiResponse<Pharmacy> switchPharmacy(@RequestBody Map<String, Long> request) {
        try {
            Long pharmacyId = request.get("pharmacyId");
            if (pharmacyId == null) {
                return ApiResponse.badRequest("药店ID不能为空");
            }

            // 验证药店是否存在
            Pharmacy pharmacy = pharmacyService.getPharmacyById(pharmacyId);
            if (pharmacy == null) {
                return ApiResponse.badRequest("药店不存在");
            }

            // 验证药店是否启用
            if (!pharmacy.getIsActive()) {
                return ApiResponse.badRequest("无法选择已停用的药店");
            }

            // 验证用户是否有权限访问该药店
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            List<Pharmacy> userPharmacies = pharmacyService.getPharmaciesForUser(username);
            
            boolean hasPermission = userPharmacies.stream()
                .anyMatch(p -> p.getId().equals(pharmacyId));
            
            if (!hasPermission) {
                return ApiResponse.forbidden("您没有权限访问该药店");
            }

            // 更新当前药店上下文
            pharmacyContext.setCurrentPharmacy(pharmacy);
            
            log.info("用户 {} 成功切换到药店: {} (ID: {})", username, pharmacy.getName(), pharmacyId);
            return ApiResponse.success("药店切换成功", pharmacy);
            
        } catch (Exception e) {
            log.error("切换药店失败: {}", e.getMessage());
            return ApiResponse.error("切换药店失败: " + e.getMessage());
        }
    }

    /**
     * 创建药店（管理员权限）
     */
    @PostMapping
    public ApiResponse<Pharmacy> createPharmacy(@RequestBody Pharmacy pharmacy) {
        try {
            Pharmacy savedPharmacy = pharmacyService.savePharmacy(pharmacy);
            log.info("创建药店成功: {} (ID: {})", savedPharmacy.getName(), savedPharmacy.getId());
            return ApiResponse.success("药店创建成功", savedPharmacy);
        } catch (Exception e) {
            log.error("创建药店失败: {}", e.getMessage());
            return ApiResponse.error("创建药店失败: " + e.getMessage());
        }
    }

    /**
     * 更新药店（管理员权限）
     */
    @PutMapping("/{id}")
    public ApiResponse<Pharmacy> updatePharmacy(@PathVariable Long id, @RequestBody Pharmacy pharmacy) {
        try {
            pharmacy.setId(id);
            Pharmacy updatedPharmacy = pharmacyService.savePharmacy(pharmacy);
            
            // 如果更新的是当前选中的药店，需要更新上下文
            Pharmacy currentPharmacy = pharmacyContext.getCurrentPharmacy();
            if (currentPharmacy != null && currentPharmacy.getId().equals(id)) {
                pharmacyContext.setCurrentPharmacy(updatedPharmacy);
            }
            
            log.info("更新药店成功: {} (ID: {})", updatedPharmacy.getName(), id);
            return ApiResponse.success("药店更新成功", updatedPharmacy);
        } catch (Exception e) {
            log.error("更新药店失败: {}", e.getMessage());
            return ApiResponse.error("更新药店失败: " + e.getMessage());
        }
    }

    /**
     * 删除药店（管理员权限）
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deletePharmacy(@PathVariable Long id) {
        try {
            // 检查是否是当前选中的药店
            Pharmacy currentPharmacy = pharmacyContext.getCurrentPharmacy();
            if (currentPharmacy != null && currentPharmacy.getId().equals(id)) {
                pharmacyContext.setCurrentPharmacy(null);
            }
            
            pharmacyService.deletePharmacy(id);
            log.info("删除药店成功: ID {}", id);
            return ApiResponse.success("药店删除成功");
        } catch (Exception e) {
            log.error("删除药店失败: {}", e.getMessage());
            return ApiResponse.error("删除药店失败: " + e.getMessage());
        }
    }

    /**
     * 获取药店详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Pharmacy> getPharmacyById(@PathVariable Long id) {
        try {
            Pharmacy pharmacy = pharmacyService.getPharmacyById(id);
            if (pharmacy == null) {
                return ApiResponse.notFound("药店不存在");
            }
            return ApiResponse.success(pharmacy);
        } catch (Exception e) {
            log.error("获取药店详情失败: {}", e.getMessage());
            return ApiResponse.error("获取药店详情失败: " + e.getMessage());
        }
    }
}
