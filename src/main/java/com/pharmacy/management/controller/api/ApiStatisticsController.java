package com.pharmacy.management.controller.api;

import com.pharmacy.management.config.PharmacyContext;
import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.entity.Pharmacy;
import com.pharmacy.management.service.StatisticsService;
import com.pharmacy.management.service.ExcelExportService;
import com.pharmacy.management.service.PharmacyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计报表API控制器
 */
@RestController
@RequestMapping("/api/v1/statistics")
public class ApiStatisticsController {

    private static final Logger log = LoggerFactory.getLogger(ApiStatisticsController.class);

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private ExcelExportService excelExportService;

    @Autowired
    private PharmacyService pharmacyService;

    @Autowired
    private PharmacyContext pharmacyContext;

    /**
     * 获取基本统计数据
     */
    @GetMapping("/basic")
    public ApiResponse<Map<String, Object>> getBasicStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            Map<String, Object> statistics = statisticsService.getBasicStatistics(startTime, endTime, pharmacyId);
            log.info("获取基本统计数据成功: startDate={}, endDate={}, pharmacyId={}", startDate, endDate, pharmacyId);
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取基本统计数据失败: {}", e.getMessage());
            return ApiResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取性别分布数据
     */
    @GetMapping("/gender")
    public ApiResponse<Map<String, Long>> getGenderDistribution(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            Map<String, Long> distribution = statisticsService.getGenderDistribution(startTime, endTime, pharmacyId);
            log.info("获取性别分布数据成功: startDate={}, endDate={}, pharmacyId={}", startDate, endDate, pharmacyId);
            
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            log.error("获取性别分布数据失败: {}", e.getMessage());
            return ApiResponse.error("获取性别分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取年龄分布数据
     */
    @GetMapping("/age")
    public ApiResponse<Map<String, Long>> getAgeDistribution(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            Map<String, Long> distribution = statisticsService.getAgeDistribution(startTime, endTime, pharmacyId);
            log.info("获取年龄分布数据成功: startDate={}, endDate={}, pharmacyId={}", startDate, endDate, pharmacyId);
            
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            log.error("获取年龄分布数据失败: {}", e.getMessage());
            return ApiResponse.error("获取年龄分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗方式分布数据
     */
    @GetMapping("/treatment")
    public ApiResponse<Map<String, Object>> getTreatmentDistribution(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            Map<String, Object> distribution = statisticsService.getTreatmentDistribution(startTime, endTime, pharmacyId);
            log.info("获取治疗方式分布数据成功: startDate={}, endDate={}, pharmacyId={}", startDate, endDate, pharmacyId);
            
            return ApiResponse.success(distribution);
        } catch (Exception e) {
            log.error("获取治疗方式分布数据失败: {}", e.getMessage());
            return ApiResponse.error("获取治疗方式分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取趋势数据
     */
    @GetMapping("/trend")
    public ApiResponse<List<Map<String, Object>>> getTrendData(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "day") String groupBy,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            // 验证groupBy参数
            if (!isValidGroupBy(groupBy)) {
                return ApiResponse.badRequest("无效的分组参数，支持: day, week, month");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            List<Map<String, Object>> trendData = statisticsService.getTrendData(startTime, endTime, groupBy, pharmacyId);
            log.info("获取趋势数据成功: startDate={}, endDate={}, groupBy={}, pharmacyId={}", 
                startDate, endDate, groupBy, pharmacyId);
            
            return ApiResponse.success(trendData);
        } catch (Exception e) {
            log.error("获取趋势数据失败: {}", e.getMessage());
            return ApiResponse.error("获取趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出统计数据
     */
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ResponseEntity.status(403).build();
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            byte[] excelBytes = excelExportService.exportStatistics(startTime, endTime, pharmacyId);
            
            String filename = String.format("统计数据_%s至%s.xlsx", 
                    startDate.format(DateTimeFormatter.ISO_DATE),
                    endDate.format(DateTimeFormatter.ISO_DATE));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(excelBytes.length);

            log.info("导出统计数据成功: startDate={}, endDate={}, pharmacyId={}, 文件大小={}字节", 
                startDate, endDate, pharmacyId, excelBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelBytes);
        } catch (Exception e) {
            log.error("导出统计数据失败: {}", e.getMessage());
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取用户可访问的药店列表
     */
    @GetMapping("/pharmacies")
    public ApiResponse<List<Pharmacy>> getUserPharmacies() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            
            List<Pharmacy> pharmacies = pharmacyService.getPharmaciesForUser(username);
            log.info("获取用户药店列表成功: 用户={}, 药店数量={}", username, pharmacies.size());
            
            return ApiResponse.success(pharmacies);
        } catch (Exception e) {
            log.error("获取用户药店列表失败: {}", e.getMessage());
            return ApiResponse.error("获取药店列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取综合统计数据（包含多个维度）
     */
    @GetMapping("/comprehensive")
    public ApiResponse<Map<String, Object>> getComprehensiveStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long pharmacyId) {
        try {
            // 验证权限
            if (!validatePharmacyAccess(pharmacyId)) {
                return ApiResponse.forbidden("您没有权限访问该药店的统计数据");
            }

            LocalDateTime startTime = startDate.atStartOfDay();
            LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

            Map<String, Object> result = new HashMap<>();
            
            // 基本统计
            result.put("basic", statisticsService.getBasicStatistics(startTime, endTime, pharmacyId));
            
            // 性别分布
            result.put("gender", statisticsService.getGenderDistribution(startTime, endTime, pharmacyId));
            
            // 年龄分布
            result.put("age", statisticsService.getAgeDistribution(startTime, endTime, pharmacyId));
            
            // 治疗方式分布
            result.put("treatment", statisticsService.getTreatmentDistribution(startTime, endTime, pharmacyId));
            
            // 趋势数据（按天）
            result.put("trend", statisticsService.getTrendData(startTime, endTime, "day", pharmacyId));

            log.info("获取综合统计数据成功: startDate={}, endDate={}, pharmacyId={}", startDate, endDate, pharmacyId);
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取综合统计数据失败: {}", e.getMessage());
            return ApiResponse.error("获取综合统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户是否有权限访问指定药店的数据
     */
    private boolean validatePharmacyAccess(Long pharmacyId) {
        try {
            // 如果没有指定药店ID，检查是否是管理员
            if (pharmacyId == null) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                return authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            }

            // 检查用户是否有权限访问指定药店
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            List<Pharmacy> userPharmacies = pharmacyService.getPharmaciesForUser(username);
            
            return userPharmacies.stream()
                .anyMatch(p -> p.getId().equals(pharmacyId));
        } catch (Exception e) {
            log.error("验证药店访问权限失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证分组参数是否有效
     */
    private boolean isValidGroupBy(String groupBy) {
        return "day".equals(groupBy) || "week".equals(groupBy) || "month".equals(groupBy);
    }
}
