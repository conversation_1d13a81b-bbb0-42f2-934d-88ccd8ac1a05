package com.pharmacy.management.controller.api;

import com.pharmacy.management.dto.ApiResponse;
import com.pharmacy.management.dto.PageResponse;
import com.pharmacy.management.entity.QuickMedicine;
import com.pharmacy.management.enums.MedicineType;
import com.pharmacy.management.service.QuickMedicineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 快捷药品管理API控制器
 */
@RestController
@RequestMapping("/api/v1/quick-medicines")
public class ApiQuickMedicineController {

    private static final Logger log = LoggerFactory.getLogger(ApiQuickMedicineController.class);

    @Autowired
    private QuickMedicineService quickMedicineService;

    /**
     * 获取快捷药品列表（分页）
     */
    @GetMapping
    public ApiResponse<PageResponse<QuickMedicine>> getQuickMedicines(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sort,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) MedicineType type,
            @RequestParam(required = false) String keyword) {
        try {
            // 创建分页参数
            Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

            Page<QuickMedicine> medicines;
            
            if (type != null && keyword != null && !keyword.trim().isEmpty()) {
                // 按类型和关键词搜索
                medicines = quickMedicineService.findByTypeAndShortNameContainingOrderByCreatedAtDesc(type, keyword.trim(), pageable);
                log.info("搜索快捷药品，类型: {}, 关键词: {}, 结果数量: {}", type, keyword, medicines.getTotalElements());
            } else if (type != null) {
                // 按类型搜索
                medicines = quickMedicineService.findByTypeOrderByCreatedAtDesc(type, pageable);
                log.info("获取快捷药品列表，类型: {}, 结果数量: {}", type, medicines.getTotalElements());
            } else if (keyword != null && !keyword.trim().isEmpty()) {
                // 按关键词搜索
                medicines = quickMedicineService.findByShortNameContainingOrderByCreatedAtDesc(keyword.trim(), pageable);
                log.info("搜索快捷药品，关键词: {}, 结果数量: {}", keyword, medicines.getTotalElements());
            } else {
                // 获取所有药品
                medicines = quickMedicineService.findAllByOrderByCreatedAtDesc(pageable);
                log.info("获取所有快捷药品，结果数量: {}", medicines.getTotalElements());
            }

            PageResponse<QuickMedicine> response = new PageResponse<>(medicines);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取快捷药品列表失败: {}", e.getMessage());
            return ApiResponse.error("获取快捷药品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取快捷药品列表（不分页）
     */
    @GetMapping("/by-type/{type}")
    public ApiResponse<List<QuickMedicine>> getQuickMedicinesByType(@PathVariable MedicineType type) {
        try {
            List<QuickMedicine> medicines = quickMedicineService.findByType(type);
            log.info("获取快捷药品列表，类型: {}, 数量: {}", type, medicines.size());
            return ApiResponse.success(medicines);
        } catch (Exception e) {
            log.error("获取快捷药品列表失败: {}", e.getMessage());
            return ApiResponse.error("获取快捷药品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型和关键词搜索快捷药品（不分页）
     */
    @GetMapping("/search")
    public ApiResponse<List<QuickMedicine>> searchQuickMedicines(
            @RequestParam MedicineType type,
            @RequestParam String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return ApiResponse.badRequest("搜索关键词不能为空");
            }

            List<QuickMedicine> medicines = quickMedicineService.findByTypeAndKeyword(type, keyword.trim());
            log.info("搜索快捷药品，类型: {}, 关键词: {}, 结果数量: {}", type, keyword, medicines.size());
            return ApiResponse.success(medicines);
        } catch (Exception e) {
            log.error("搜索快捷药品失败: {}", e.getMessage());
            return ApiResponse.error("搜索快捷药品失败: " + e.getMessage());
        }
    }

    /**
     * 获取快捷药品详情
     */
    @GetMapping("/{id}")
    public ApiResponse<QuickMedicine> getQuickMedicineById(@PathVariable Long id) {
        try {
            QuickMedicine medicine = quickMedicineService.findById(id);
            if (medicine == null) {
                return ApiResponse.notFound("快捷药品不存在");
            }
            return ApiResponse.success(medicine);
        } catch (Exception e) {
            log.error("获取快捷药品详情失败: {}", e.getMessage());
            return ApiResponse.error("获取快捷药品详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建快捷药品
     */
    @PostMapping
    public ApiResponse<QuickMedicine> createQuickMedicine(@RequestBody QuickMedicine quickMedicine) {
        try {
            // 验证必填字段
            if (quickMedicine.getShortName() == null || quickMedicine.getShortName().trim().isEmpty()) {
                return ApiResponse.badRequest("药品简称不能为空");
            }
            if (quickMedicine.getFullName() == null || quickMedicine.getFullName().trim().isEmpty()) {
                return ApiResponse.badRequest("药品全称不能为空");
            }
            if (quickMedicine.getType() == null) {
                return ApiResponse.badRequest("药品类型不能为空");
            }
            if (quickMedicine.getUsageMethod() == null || quickMedicine.getUsageMethod().trim().isEmpty()) {
                return ApiResponse.badRequest("用法用量不能为空");
            }
            if (quickMedicine.getTotalAmount() == null || quickMedicine.getTotalAmount().trim().isEmpty()) {
                return ApiResponse.badRequest("总量不能为空");
            }

            QuickMedicine savedMedicine = quickMedicineService.save(quickMedicine);
            log.info("创建快捷药品成功: {} (ID: {})", savedMedicine.getShortName(), savedMedicine.getId());
            return ApiResponse.success("快捷药品创建成功", savedMedicine);
        } catch (Exception e) {
            log.error("创建快捷药品失败: {}", e.getMessage());
            return ApiResponse.error("创建快捷药品失败: " + e.getMessage());
        }
    }

    /**
     * 更新快捷药品
     */
    @PutMapping("/{id}")
    public ApiResponse<QuickMedicine> updateQuickMedicine(@PathVariable Long id, @RequestBody QuickMedicine quickMedicine) {
        try {
            // 检查药品是否存在
            QuickMedicine existingMedicine = quickMedicineService.findById(id);
            if (existingMedicine == null) {
                return ApiResponse.notFound("快捷药品不存在");
            }

            // 验证必填字段
            if (quickMedicine.getShortName() == null || quickMedicine.getShortName().trim().isEmpty()) {
                return ApiResponse.badRequest("药品简称不能为空");
            }
            if (quickMedicine.getFullName() == null || quickMedicine.getFullName().trim().isEmpty()) {
                return ApiResponse.badRequest("药品全称不能为空");
            }
            if (quickMedicine.getType() == null) {
                return ApiResponse.badRequest("药品类型不能为空");
            }
            if (quickMedicine.getUsageMethod() == null || quickMedicine.getUsageMethod().trim().isEmpty()) {
                return ApiResponse.badRequest("用法用量不能为空");
            }
            if (quickMedicine.getTotalAmount() == null || quickMedicine.getTotalAmount().trim().isEmpty()) {
                return ApiResponse.badRequest("总量不能为空");
            }

            // 更新药品信息
            quickMedicine.setId(id);
            quickMedicine.setCreatedAt(existingMedicine.getCreatedAt()); // 保持创建时间不变
            QuickMedicine updatedMedicine = quickMedicineService.update(quickMedicine);
            
            log.info("更新快捷药品成功: {} (ID: {})", updatedMedicine.getShortName(), id);
            return ApiResponse.success("快捷药品更新成功", updatedMedicine);
        } catch (Exception e) {
            log.error("更新快捷药品失败: {}", e.getMessage());
            return ApiResponse.error("更新快捷药品失败: " + e.getMessage());
        }
    }

    /**
     * 删除快捷药品
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteQuickMedicine(@PathVariable Long id) {
        try {
            // 检查药品是否存在
            QuickMedicine medicine = quickMedicineService.findById(id);
            if (medicine == null) {
                return ApiResponse.notFound("快捷药品不存在");
            }

            quickMedicineService.delete(id);
            log.info("删除快捷药品成功: ID {}", id);
            return ApiResponse.success("快捷药品删除成功");
        } catch (Exception e) {
            log.error("删除快捷药品失败: {}", e.getMessage());
            return ApiResponse.error("删除快捷药品失败: " + e.getMessage());
        }
    }

    /**
     * 获取药品类型列表
     */
    @GetMapping("/types")
    public ApiResponse<Map<String, Object>> getMedicineTypes() {
        try {
            Map<String, Object> types = new HashMap<>();
            for (MedicineType type : MedicineType.values()) {
                types.put(type.name(), type.getDescription());
            }
            return ApiResponse.success(types);
        } catch (Exception e) {
            log.error("获取药品类型列表失败: {}", e.getMessage());
            return ApiResponse.error("获取药品类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取快捷药品统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getQuickMedicineStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 统计各类型药品数量
            for (MedicineType type : MedicineType.values()) {
                List<QuickMedicine> medicines = quickMedicineService.findByType(type);
                statistics.put(type.name().toLowerCase() + "Count", medicines.size());
            }
            
            // 总数量
            Page<QuickMedicine> allMedicines = quickMedicineService.findAllByOrderByCreatedAtDesc(PageRequest.of(0, 1));
            statistics.put("totalCount", allMedicines.getTotalElements());
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取快捷药品统计信息失败: {}", e.getMessage());
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
